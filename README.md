# FastTime

FastTime is a modern Intermittent Fasting application with comprehensive body data tracking. Designed for users who want to manage their fasting schedules and monitor their health metrics, FastTime offers an intuitive interface, insightful analytics, and seamless integration with popular authentication providers.

## Features

- **Intermittent Fasting Timer:** Start, pause, and track your fasting sessions with ease.
- **Body Data Tracking:** Log and visualize weight, body fat, and other health metrics over time.
- **Authentication:** Sign in with Google, Apple, or email for secure access.
- **Reminders & Notifications:** Stay on track with customizable fasting reminders.
- **Analytics Dashboard:** View trends and progress with beautiful charts and summaries.
- **Cross-Platform:** Available on Android and iOS.

## Screenshots

<!-- Add screenshots here if available -->

## Getting Started

### Prerequisites
- [Flutter](https://flutter.dev/docs/get-started/install) (latest stable)
- Dart SDK
- Firebase account (for authentication and data storage)

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/fasttime.git
   cd fasttime
   ```
2. **Install dependencies:**
   ```bash
   flutter pub get
   ```
3. **Configure Firebase:**
   - Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS) to the respective platform folders.
   - Update `lib/firebase_options.dart` as needed.
4. **Run the app:**
   ```bash
   flutter run
   ```

## Folder Structure

- `lib/` - Main application code
  - `blocs/` - State management
  - `dialogs/` - Custom dialogs
  - `models/` - Data models
  - `route/` - App routing
  - `screens/` - UI screens
  - `services/` - Business logic and integrations
  - `widgets/` - Reusable widgets
- `assets/` - Images, icons, and other assets

## Contributing

Contributions are welcome! Please open issues and submit pull requests for new features, bug fixes, or improvements.

## License

[MIT License](LICENSE)

## Acknowledgements

- [Flutter](https://flutter.dev/)
- [Firebase](https://firebase.google.com/)
- [Rive](https://rive.app/) for animations

---

*Made with ❤️ by Allen.*
