
class FastingProtocol {
  final String id;
  final String name;
  final int hours;
  final String description;

  const FastingProtocol({
    required this.id,
    required this.name,
    required this.hours,
    required this.description,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FastingProtocol &&
        other.id == id &&
        other.name == name &&
        other.hours == hours &&
        other.description == description;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ hours.hashCode ^ description.hashCode;

  FastingProtocol copyWith({
    String? id,
    String? name,
    int? hours,
    String? description,
  }) {
    return FastingProtocol(
      id: id ?? this.id,
      name: name ?? this.name,
      hours: hours ?? this.hours,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'hours': hours,
      'description': description,
    };
  }

  factory FastingProtocol.fromMap(Map<String, dynamic> map) {
    return FastingProtocol(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      hours: map['hours'] ?? 0,
      description: map['description'] ?? '',
    );
  }
}