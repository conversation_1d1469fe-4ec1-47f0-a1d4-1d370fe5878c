import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'achievement.g.dart';

@HiveType(typeId: 2)
class Achievement extends HiveObject with EquatableMixin {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String iconName;

  @HiveField(4)
  final DateTime unlockedDate;

  @HiveField(5)
  final int points;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.unlockedDate,
    this.points = 10,
  });

  @override
  List<Object?> get props =>
      [id, title, description, iconName, unlockedDate, points];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconName': iconName,
      'unlockedDate': unlockedDate.toIso8601String(),
      'points': points,
    };
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      iconName: json['iconName'],
      unlockedDate: DateTime.parse(json['unlockedDate']),
      points: json['points'],
    );
  }
}
