import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'fasting_day.g.dart';

@HiveType(typeId: 0)
class FastingDay extends HiveObject with EquatableMixin {
  @HiveField(0)
  final DateTime date;

  @HiveField(1)
  final String startTime;

  @HiveField(2)
  final String endTime;

  @HiveField(3)
  final bool isActive;

  @HiveField(4)
  final bool completed;

  @HiveField(5)
  final DateTime? startDate;

  @HiveField(6)
  final DateTime? endDate;

  @HiveField(7)
  final int? actualFastingMinutes;

  @HiveField(8)
  final String fastingProtocol;

  @HiveField(9)
  final double waterIntake;

  @HiveField(10)
  final int steps;

  @HiveField(11)
  final double exercise;

  @HiveField(12)
  final List<String> achievements;

  @HiveField(13)
  final String id;

  FastingDay({
    required this.id,
    required this.date,
    this.startTime = '--:--',
    this.endTime = '--:--',
    this.isActive = false,
    this.completed = false,
    this.startDate,
    this.endDate,
    this.actualFastingMinutes,
    this.fastingProtocol = '16:8',
    this.waterIntake = 0.0,
    this.steps = 0,
    this.exercise = 0.0,
    this.achievements = const [],
  });

  FastingDay copyWith({
    String? id,
    DateTime? date,
    String? startTime,
    String? endTime,
    bool? isActive,
    bool? completed,
    DateTime? startDate,
    DateTime? endDate,
    int? actualFastingMinutes,
    String? fastingProtocol,
    double? waterIntake,
    int? steps,
    double? exercise,
    List<String>? achievements,
  }) {
    return FastingDay(
      id: id ?? this.id,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isActive: isActive ?? this.isActive,
      completed: completed ?? this.completed,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      actualFastingMinutes: actualFastingMinutes ?? this.actualFastingMinutes,
      fastingProtocol: fastingProtocol ?? this.fastingProtocol,
      waterIntake: waterIntake ?? this.waterIntake,
      steps: steps ?? this.steps,
      exercise: exercise ?? this.exercise,
      achievements: achievements ?? this.achievements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        startTime,
        endTime,
        isActive,
        completed,
        startDate,
        endDate,
        actualFastingMinutes,
        fastingProtocol,
        waterIntake,
        steps,
        exercise,
        achievements,
      ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'startTime': startTime,
      'endTime': endTime,
      'isActive': isActive,
      'completed': completed,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'actualFastingMinutes': actualFastingMinutes,
      'fastingProtocol': fastingProtocol,
      'waterIntake': waterIntake,
      'steps': steps,
      'exercise': exercise,
      'achievements': achievements,
    };
  }

  factory FastingDay.fromJson(Map<String, dynamic> json) {
    return FastingDay(
      id: json['id'] ?? '',
      date: DateTime.parse(json['date']),
      startTime: json['startTime'] ?? '--:--',
      endTime: json['endTime'] ?? '--:--',
      isActive: json['isActive'] ?? false,
      completed: json['completed'] ?? false,
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      actualFastingMinutes: json['actualFastingMinutes'] ?? 0,
      fastingProtocol: json['fastingProtocol'] ?? '16:8',
      waterIntake: json['waterIntake'] ?? 0.0,
      steps: json['steps'] ?? 0,
      exercise: json['exercise'] ?? 0.0,
      achievements: (json['achievements'] as List?)?.cast<String>() ?? [],
    );
  }
}
