import 'package:hive/hive.dart';

part 'body_metric.g.dart';

@HiveType(typeId: 1)
class BodyMetric extends HiveObject {
  @HiveField(0)
  final DateTime date;

  @HiveField(1)
  final double weight;

  @HiveField(2)
  final double height;

  BodyMetric({
    required this.date,
    required this.weight,
    required this.height,
  });

  double get bmi => weight / ((height / 100) * (height / 100));

  BodyMetric copyWith({
    DateTime? date,
    double? weight,
    double? height,
  }) {
    return BodyMetric(
      date: date ?? this.date,
      weight: weight ?? this.weight,
      height: height ?? this.height,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'weight': weight,
      'height': height,
    };
  }

  factory BodyMetric.fromJson(Map<String, dynamic> json) {
    return BodyMetric(
      date: DateTime.parse(json['date']),
      weight: json['weight'],
      height: json['height'],
    );
  }
  @override
  String toString() {
    return 'BodyMetric(date: $date, weight: $weight, height: $height)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BodyMetric &&
        other.date == date &&
        other.weight == weight &&
        other.height == height;
  }

  @override
  int get hashCode => date.hashCode ^ weight.hashCode ^ height.hashCode;
}
