// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'body_metric.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BodyMetricAdapter extends TypeAdapter<BodyMetric> {
  @override
  final int typeId = 1;

  @override
  BodyMetric read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BodyMetric(
      date: fields[0] as DateTime,
      weight: fields[1] as double,
      height: fields[2] as double,
    );
  }

  @override
  void write(BinaryWriter writer, BodyMetric obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.weight)
      ..writeByte(2)
      ..write(obj.height);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BodyMetricAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
