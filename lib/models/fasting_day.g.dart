// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fasting_day.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FastingDayAdapter extends TypeAdapter<FastingDay> {
  @override
  final int typeId = 0;

  @override
  FastingDay read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FastingDay(
      id: fields[13] as String,
      date: fields[0] as DateTime,
      startTime: fields[1] as String,
      endTime: fields[2] as String,
      isActive: fields[3] as bool,
      completed: fields[4] as bool,
      startDate: fields[5] as DateTime?,
      endDate: fields[6] as DateTime?,
      actualFastingMinutes: fields[7] as int?,
      fastingProtocol: fields[8] as String,
      waterIntake: fields[9] as double,
      steps: fields[10] as int,
      exercise: fields[11] as double,
      achievements: (fields[12] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, FastingDay obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.startTime)
      ..writeByte(2)
      ..write(obj.endTime)
      ..writeByte(3)
      ..write(obj.isActive)
      ..writeByte(4)
      ..write(obj.completed)
      ..writeByte(5)
      ..write(obj.startDate)
      ..writeByte(6)
      ..write(obj.endDate)
      ..writeByte(7)
      ..write(obj.actualFastingMinutes)
      ..writeByte(8)
      ..write(obj.fastingProtocol)
      ..writeByte(9)
      ..write(obj.waterIntake)
      ..writeByte(10)
      ..write(obj.steps)
      ..writeByte(11)
      ..write(obj.exercise)
      ..writeByte(12)
      ..write(obj.achievements)
      ..writeByte(13)
      ..write(obj.id);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FastingDayAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
