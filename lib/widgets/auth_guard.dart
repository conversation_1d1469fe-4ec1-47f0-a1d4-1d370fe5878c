import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../screens/home/<USER>';
import '../screens/splash/splash_screen.dart';
import '../services/auth_persistence_service.dart';

/// AuthGuard widget that determines the initial route based on authentication status
class AuthGuard extends StatefulWidget {
  const AuthGuard({super.key});

  @override
  State<AuthGuard> createState() => _AuthGuardState();
}

class _AuthGuardState extends State<AuthGuard> {
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  Future<void> _checkAuthAndNavigate() async {
    // Small delay to ensure BlocProvider is ready
    await Future.delayed(const Duration(milliseconds: 100));
    
    if (!mounted || _hasNavigated) return;

    final authBloc = context.read<AuthBloc>();
    final currentState = authBloc.state;

    if (currentState is Authenticated) {
      // User is already authenticated, go directly to home
      _navigateToHome();
    } else if (currentState is AuthInitial || currentState is AuthLoading) {
      // Wait for auth check to complete
      _waitForAuthCheck();
    } else {
      // User is not authenticated, show splash screen
      _navigateToSplash();
    }
  }

  void _waitForAuthCheck() {
    // Listen for auth state changes
    final subscription = context.read<AuthBloc>().stream.listen((state) {
      if (!mounted || _hasNavigated) return;

      if (state is Authenticated) {
        _navigateToHome();
      } else if (state is Unauthenticated || state is AuthFailure) {
        _navigateToSplash();
      }
    });

    // Timeout after 3 seconds to prevent infinite waiting
    Future.delayed(const Duration(seconds: 3), () {
      subscription.cancel();
      if (!mounted || _hasNavigated) return;
      _navigateToSplash();
    });
  }

  void _navigateToHome() {
    if (_hasNavigated) return;
    _hasNavigated = true;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.go('/home');
      }
    });
  }

  void _navigateToSplash() {
    if (_hasNavigated) return;
    _hasNavigated = true;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.go('/splash');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (_hasNavigated) return;

        if (state is Authenticated) {
          _navigateToHome();
        } else if (state is Unauthenticated || state is AuthFailure) {
          _navigateToSplash();
        }
      },
      child: const Scaffold(
        backgroundColor: Color(0xFF0A0E21),
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6B4EFF)),
          ),
        ),
      ),
    );
  }
}
