import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;

import 'package:google_fonts/google_fonts.dart';

class ModernTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final DateTime? initialDate; // New parameter
  final String label;
  final Function(TimeOfDay, DateTime) onTimeSelected; // Updated to include date
  final Color accentColor;
  final Color backgroundColor;
  final DateTime? firstDate; // New parameter
  final DateTime? lastDate; // New parameter

  const ModernTimePicker({
    Key? key,
    required this.initialTime,
    this.initialDate,
    required this.label,
    required this.onTimeSelected,
    this.accentColor = const Color(0xFF6B4EFF),
    this.backgroundColor = const Color(0xFF191919),
    this.firstDate,
    this.lastDate,
  }) : super(key: key);

  @override
  State<ModernTimePicker> createState() => _ModernTimePickerState();
}

class _ModernTimePickerState extends State<ModernTimePicker>
    with SingleTickerProviderStateMixin {
  late TimeOfDay _selectedTime;
  late DateTime _selectedDate;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _selectingHour = true;
  final ScrollController _dateScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.initialTime;
    _selectedDate = widget.initialDate ?? DateTime.now();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _animationController.forward();

    // Scroll to center after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate();
    });
  }

  @override
  void dispose() {
    _dateScrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _scrollToSelectedDate() {
    // Calculate the offset to center the current date
    final today = DateTime.now();
    final difference = _selectedDate.difference(today).inDays +
        7; // Add 7 because we start 7 days before
    final offset = difference *
        72.0; // 72 is the total width of each date item (64 + 8 margin)

    if (_dateScrollController.hasClients) {
      _dateScrollController.animateTo(
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _updateTime(int hour, int minute) {
    setState(() {
      _selectedTime = TimeOfDay(hour: hour, minute: minute);
    });
  }

  void _toggleTimeMode() {
    setState(() {
      _selectingHour = !_selectingHour;
    });
  }

  String _formatHour(int hour) {
    return hour == 0
        ? '12'
        : (hour > 12 ? (hour - 12).toString() : hour.toString());
  }

  String _formatMinute(int minute) {
    return minute.toString().padLeft(2, '0');
  }

  String _getPeriod() {
    return _selectedTime.hour >= 12 ? 'PM' : 'AM';
  }

  void _togglePeriod() {
    setState(() {
      if (_selectedTime.hour >= 12) {
        // Switch to AM
        _selectedTime = TimeOfDay(
            hour: _selectedTime.hour - 12, minute: _selectedTime.minute);
      } else {
        // Switch to PM
        _selectedTime = TimeOfDay(
            hour: _selectedTime.hour + 12, minute: _selectedTime.minute);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.backgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _animation,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 40),
                _buildTimeDisplay(),
                const SizedBox(height: 40),
                Expanded(
                  child: _buildTimePicker(),
                ),
                _buildBottomButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.label,
              style: GoogleFonts.dmSans(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(Icons.close, color: Colors.white),
            ),
          ],
        ),
        const SizedBox(height: 20),
        _buildDateSelector(),
      ],
    );
  }

  Widget _buildDateSelector() {
    final today = DateTime.now();
    final currentDate = DateTime(today.year, today.month, today.day);

    return SizedBox(
      height: MediaQuery.of(context).size.width * 0.2,
      child: ListView.builder(
        controller: _dateScrollController,
        scrollDirection: Axis.horizontal,
        itemCount: 15, // Show 15 days
        itemBuilder: (context, index) {
          final date =
              today.subtract(Duration(days: 7 - index)); // Center current day
          final isSelected = date.year == _selectedDate.year &&
              date.month == _selectedDate.month &&
              date.day == _selectedDate.day;
          final isToday = date.year == currentDate.year &&
              date.month == currentDate.month &&
              date.day == currentDate.day;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });
            },
            child: Container(
              width: MediaQuery.of(context).size.width *
                  0.1, // Fixed width for consistent scrolling
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? widget.accentColor : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected
                      ? widget.accentColor
                      : isToday
                          ? widget.accentColor.withOpacity(0.5)
                          : Colors.white54,
                  width: isToday ? 2 : 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _getDayName(date),
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : isToday
                              ? widget.accentColor
                              : Colors.white54,
                      fontSize: 12,
                      fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    date.day.toString(),
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : isToday
                              ? widget.accentColor
                              : Colors.white54,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isToday) const SizedBox(height: 2),
                  if (isToday)
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? Colors.white : widget.accentColor,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String _getDayName(DateTime date) {
    switch (date.weekday) {
      case DateTime.monday:
        return 'MON';
      case DateTime.tuesday:
        return 'TUE';
      case DateTime.wednesday:
        return 'WED';
      case DateTime.thursday:
        return 'THU';
      case DateTime.friday:
        return 'FRI';
      case DateTime.saturday:
        return 'SAT';
      case DateTime.sunday:
        return 'SUN';
      default:
        return '';
    }
  }

  Widget _buildTimeDisplay() {
    final hourStyle = TextStyle(
      fontSize: 60,
      fontWeight: FontWeight.w300,
      color: _selectingHour ? widget.accentColor : Colors.white,
      letterSpacing: -1,
    );

    final minuteStyle = TextStyle(
      fontSize: 60,
      fontWeight: FontWeight.w300,
      color: !_selectingHour ? widget.accentColor : Colors.white,
      letterSpacing: -1,
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            if (!_selectingHour) _toggleTimeMode();
          },
          child: Text(
            _formatHour(_selectedTime.hour),
            style: hourStyle,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            ":",
            style: TextStyle(
              fontSize: 60,
              fontWeight: FontWeight.w200,
              color: Colors.white,
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            if (_selectingHour) _toggleTimeMode();
          },
          child: Text(
            _formatMinute(_selectedTime.minute),
            style: minuteStyle,
          ),
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: _togglePeriod,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
            decoration: BoxDecoration(
              color: widget.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getPeriod(),
              style: TextStyle(
                color: widget.accentColor,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimePicker() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 0.2),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      },
      child: _selectingHour ? _buildHourSelector() : _buildMinuteSelector(),
    );
  }

  Widget _buildHourSelector() {
    return Container(
      key: const ValueKey('hourSelector'),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1.5,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          final hour = index == 0 ? 12 : index;
          final displayHour = hour;

          // Convert display hour to 24-hour format for internal logic
          final internalHour = _getPeriod() == 'AM'
              ? (hour == 12 ? 0 : hour)
              : (hour == 12 ? 12 : hour + 12);

          final isSelected = internalHour == _selectedTime.hour;

          return GestureDetector(
            onTap: () {
              _updateTime(internalHour, _selectedTime.minute);
              Future.delayed(const Duration(milliseconds: 300), () {
                _toggleTimeMode(); // Switch to minute selection after hour is picked
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: isSelected ? widget.accentColor : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: isSelected
                    ? null
                    : Border.all(color: Colors.white24, width: 1),
              ),
              child: Center(
                child: Text(
                  displayHour.toString(),
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white54,
                    fontSize: 20,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMinuteSelector() {
    return Container(
      key: const ValueKey('minuteSelector'),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1.5,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          final minute = index * 5;
          final isSelected = minute == _selectedTime.minute;

          return GestureDetector(
            onTap: () {
              _updateTime(_selectedTime.hour, minute);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: isSelected ? widget.accentColor : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: isSelected
                    ? null
                    : Border.all(color: Colors.white24, width: 1),
              ),
              child: Center(
                child: Text(
                  minute.toString().padLeft(2, '0'),
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white54,
                    fontSize: 20,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => context.pop(),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: () {
            // Call the callback with both selected time and date
            widget.onTimeSelected(_selectedTime, _selectedDate);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.accentColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'Confirm',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
