import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class ModernDatePicker extends StatefulWidget {
  final DateTime initialDate;
  final String label;
  final Function(DateTime) onDateSelected;
  final Color accentColor;
  final Color backgroundColor;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const ModernDatePicker({
    Key? key,
    required this.initialDate,
    required this.label,
    required this.onDateSelected,
    this.accentColor = const Color(0xFF6B4EFF),
    this.backgroundColor = const Color(0xFF191919),
    this.firstDate,
    this.lastDate,
  }) : super(key: key);

  static Future<DateTime?> show({
    required BuildContext context,
    required DateTime initialDate,
    required String label,
    Color accentColor = const Color(0xFF6B4EFF),
    Color backgroundColor = const Color(0xFF191919),
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FractionallySizedBox(
        heightFactor: 0.7,
        child: ModernDatePicker(
          initialDate: initialDate,
          label: label,
          onDateSelected: (date) {
            // Use GoRouter to pop instead of Navigator
            GoRouter.of(context).pop<DateTime>(date);
          },
          accentColor: accentColor,
          backgroundColor: backgroundColor,
          firstDate: firstDate,
          lastDate: lastDate,
        ),
      ),
    );
  }

  @override
  State<ModernDatePicker> createState() => _ModernDatePickerState();
}

class _ModernDatePickerState extends State<ModernDatePicker>
    with SingleTickerProviderStateMixin {
  late DateTime _selectedDate;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: SafeArea(
        child: FadeTransition(
          opacity: _animation,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24.0, 16.0, 24.0, 24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHandle(),
                const SizedBox(height: 16),
                _buildHeader(),
                const SizedBox(height: 24),
                _buildSelectedDateDisplay(),
                const SizedBox(height: 24),
                Expanded(
                  child: _buildCalendar(),
                ),
                _buildBottomButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHandle() {
    return Center(
      child: Container(
        width: 40,
        height: 5,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(2.5),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.label,
          style: GoogleFonts.dmSans(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () => GoRouter.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.white.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedDateDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: widget.accentColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: widget.accentColor.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_rounded,
            color: widget.accentColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Selected Date',
                style: GoogleFonts.dmSans(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${_getMonthName(_selectedDate.month)} ${_selectedDate.day}, ${_selectedDate.year}',
                style: GoogleFonts.dmSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  Widget _buildCalendar() {
    return Column(
      children: [
        _buildMonthSelector(),
        const SizedBox(height: 16),
        Expanded(
          child: _buildDaysGrid(),
        ),
      ],
    );
  }

  Widget _buildMonthSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _selectedDate = DateTime(
                _selectedDate.year,
                _selectedDate.month - 1,
                _selectedDate.day,
              );
            });
          },
          icon: Icon(Icons.chevron_left, color: widget.accentColor),
        ),
        Text(
          '${_getMonthName(_selectedDate.month)} ${_selectedDate.year}',
          style: GoogleFonts.dmSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _selectedDate = DateTime(
                _selectedDate.year,
                _selectedDate.month + 1,
                _selectedDate.day,
              );
            });
          },
          icon: Icon(Icons.chevron_right, color: widget.accentColor),
        ),
      ],
    );
  }

  Widget _buildDaysGrid() {
    final daysInMonth =
        DateTime(_selectedDate.year, _selectedDate.month + 1, 0).day;
    final firstDayOfMonth =
        DateTime(_selectedDate.year, _selectedDate.month, 1);
    final firstWeekdayOfMonth =
        firstDayOfMonth.weekday % 7; // 0 = Sunday, 6 = Saturday

    return Column(
      children: [
        // Weekday headers
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _weekdayHeader('S'),
            _weekdayHeader('M'),
            _weekdayHeader('T'),
            _weekdayHeader('W'),
            _weekdayHeader('T'),
            _weekdayHeader('F'),
            _weekdayHeader('S'),
          ],
        ),
        const SizedBox(height: 12),
        Expanded(
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.2,
            ),
            itemCount: 42, // 6 rows of 7 days
            itemBuilder: (context, index) {
              final dayOffset = index - firstWeekdayOfMonth;
              final day = dayOffset + 1;

              if (dayOffset < 0 || day > daysInMonth) {
                return const SizedBox(); // Empty cell
              }

              final date =
                  DateTime(_selectedDate.year, _selectedDate.month, day);
              final isSelected = date.year == _selectedDate.year &&
                  date.month == _selectedDate.month &&
                  date.day == _selectedDate.day;
              final isToday = _isToday(date);

              // Check if date is within allowed range
              final isDisabled = (widget.firstDate != null &&
                      date.isBefore(widget.firstDate!)) ||
                  (widget.lastDate != null && date.isAfter(widget.lastDate!));

              return GestureDetector(
                onTap: isDisabled
                    ? null
                    : () {
                        setState(() {
                          _selectedDate = date;
                        });
                      },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? widget.accentColor
                        : isToday
                            ? widget.accentColor.withOpacity(0.1)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isToday && !isSelected
                        ? Border.all(color: widget.accentColor, width: 1)
                        : null,
                  ),
                  child: Center(
                    child: Text(
                      day.toString(),
                      style: GoogleFonts.dmSans(
                        color: isDisabled
                            ? Colors.grey.withOpacity(0.5)
                            : isSelected
                                ? Colors.white
                                : Colors.white.withOpacity(0.9),
                        fontWeight: isSelected || isToday
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _weekdayHeader(String text) {
    return Text(
      text,
      style: GoogleFonts.dmSans(
        color: Colors.white.withOpacity(0.7),
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Widget _buildBottomButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => GoRouter.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(
                color: Colors.white.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: () {
              widget.onDateSelected(_selectedDate);
              GoRouter.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
