import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../blocs/settings/settings_bloc.dart';
import '../blocs/settings/settings_event.dart';
import '../blocs/fasting/fasting_bloc.dart';
import '../blocs/fasting/fasting_event.dart';
import '../screens/home/<USER>';
import '../screens/splash/splash_screen.dart';

import '../utils/memory_optimizer.dart';

/// A wrapper widget that handles authentication state and proper initialization
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _hasInitialized = false;
  bool _isDataPreloaded = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is Authenticated && !_hasInitialized) {
          _initializeUserData();
          _hasInitialized = true;
        } else if (state is Unauthenticated) {
          _hasInitialized = false;
          _isDataPreloaded = false;
        }
      },
      builder: (context, state) {
        if (state is AuthInitial || state is AuthLoading) {
          return const SplashScreen();
        } else if (state is Authenticated) {
          // Preload data before showing home page
          if (!_isDataPreloaded) {
            _preloadData();
            return const SplashScreen();
          }
          return const HomePage();
        } else {
          // Navigate to splash screen for unauthenticated users
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.go('/splash');
            }
          });
          return const SplashScreen();
        }
      },
    );
  }

  void _preloadData() {
    // Preload fasting data to avoid loading state in home page
    Future.delayed(const Duration(milliseconds: 500), () async {
      if (mounted) {
        try {
          // Load current fasting day data
          context.read<FastingBloc>().add(const LoadCurrentFastingDay());

          // Wait a bit for data to load
          await Future.delayed(const Duration(milliseconds: 300));

          if (mounted) {
            setState(() {
              _isDataPreloaded = true;
            });
          }
        } catch (e) {
          debugPrint('Error preloading data: $e');
          // Even if preloading fails, show the home page
          if (mounted) {
            setState(() {
              _isDataPreloaded = true;
            });
          }
        }
      }
    });
  }

  void _initializeUserData() {
    // Initialize settings first with a small delay
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        try {
          context.read<SettingsBloc>().add(LoadSettings());
        } catch (e) {
          debugPrint('Error initializing settings: $e');
        }
      }
    });

    // Memory optimization for better performance
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        try {
          // Force memory optimization to ensure good performance
          MemoryOptimizer().forceEnterLowMemoryMode();
          debugPrint('Memory optimization applied during initialization');
        } catch (e) {
          debugPrint('Error during memory optimization: $e');
        }
      }
    });
  }
}
