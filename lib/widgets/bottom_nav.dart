import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../route/router_constants.dart';

class BottomNav extends StatelessWidget {
  final String currentRoute;

  const BottomNav({super.key, required this.currentRoute});

  int _getCurrentIndex() {
    switch (currentRoute) {
      case RouteConstants.home:
        return 0;
      case RouteConstants.bodyMetrics:
        return 1;
      case RouteConstants.progress:
        return 2;
      case RouteConstants.achievements:
        return 3;
      default:
        return 0;
    }
  }

  void _onItemTapped(BuildContext context, int index) {
    String routeName;
    switch (index) {
      case 0:
        routeName = RouteConstants.home;
        break;
      case 1:
        routeName = RouteConstants.bodyMetrics;
        break;
      case 2:
        routeName = RouteConstants.progress;
        break;
      case 3:
        routeName = RouteConstants.achievements;
        break;
      default:
        routeName = RouteConstants.home;
    }

    if (currentRoute != routeName) {
      GoRouter.of(context).goNamed(routeName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      backgroundColor: Colors.transparent,
      elevation: 0,
      currentIndex: _getCurrentIndex(),
      onTap: (index) => _onItemTapped(context, index),
      selectedItemColor: const Color(0xFF448AFF),
      unselectedItemColor: Colors.white.withValues(alpha: 0.6),
      selectedFontSize: 12,
      unselectedFontSize: 12,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home_rounded), label: 'Home'),
        BottomNavigationBarItem(
          icon: Icon(Icons.accessibility_new_rounded),
          label: 'Metrics',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.history_rounded),
          label: 'Progress',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.emoji_events_rounded),
          label: 'Rewards',
        ),
      ],
    );
  }
}
