import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/fasting_day.dart';

class FastingCalendar extends StatefulWidget {
  final List<FastingDay> fastingData;
  final DateTime selectedDate;
  final Function(DateTime) onDateSelected;
  final Color accentColor;
  final Color backgroundColor;

  const FastingCalendar({
    super.key,
    required this.fastingData,
    required this.selectedDate,
    required this.onDateSelected,
    this.accentColor = const Color(0xFF6B4EFF),
    this.backgroundColor = const Color(0xFF191919),
  });

  @override
  State<FastingCalendar> createState() => _FastingCalendarState();
}

class _FastingCalendarState extends State<FastingCalendar> {
  late DateTime _currentMonth;

  @override
  void initState() {
    super.initState();
    _currentMonth = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildMonthSelector(),
            const SizedBox(height: 16),
            _buildWeekdayHeaders(),
            const SizedBox(height: 8),
            _buildCalendarGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month - 1,
              );
            });
          },
          icon: Icon(Icons.chevron_left, color: widget.accentColor),
        ),
        Text(
          '${_getMonthName(_currentMonth.month)} ${_currentMonth.year}',
          style: GoogleFonts.dmSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(
                _currentMonth.year,
                _currentMonth.month + 1,
              );
            });
          },
          icon: Icon(Icons.chevron_right, color: widget.accentColor),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: weekdays.map((day) => _buildWeekdayHeader(day)).toList(),
    );
  }

  Widget _buildWeekdayHeader(String day) {
    return SizedBox(
      width: 32,
      child: Center(
        child: Text(
          day,
          style: GoogleFonts.dmSans(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final daysInMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    ).day;
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final firstWeekdayOfMonth =
        firstDayOfMonth.weekday % 7; // 0 = Sunday, 6 = Saturday

    return SizedBox(
      height: 240, // Fixed height for 6 rows
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1.0,
        ),
        itemCount: 42, // 6 rows of 7 days
        itemBuilder: (context, index) {
          final dayOffset = index - firstWeekdayOfMonth;
          final day = dayOffset + 1;

          if (dayOffset < 0 || day > daysInMonth) {
            return const SizedBox(); // Empty cell
          }

          final date = DateTime(_currentMonth.year, _currentMonth.month, day);
          return _buildCalendarDay(date);
        },
      ),
    );
  }

  Widget _buildCalendarDay(DateTime date) {
    final isSelected = _isSameDay(date, widget.selectedDate);
    final isToday = _isSameDay(date, DateTime.now());
    final fastingDay = _getFastingDataForDate(date);
    final hasData = fastingDay != null;

    // Calculate completion status
    bool isCompleted = false;
    double completionPercentage = 0.0;

    if (hasData) {
      final targetHours = int.parse(fastingDay.fastingProtocol.split(':')[0]);
      final actualMinutes = fastingDay.actualFastingMinutes ?? 0;
      completionPercentage = (actualMinutes / (targetHours * 60)).clamp(
        0.0,
        1.0,
      );
      isCompleted = completionPercentage >= 1.0;
    }

    return GestureDetector(
      onTap: () => widget.onDateSelected(date),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: _getDayBackgroundColor(
            isSelected,
            isToday,
            hasData,
            isCompleted,
          ),
          borderRadius: BorderRadius.circular(8),
          border: _getDayBorder(isSelected, isToday, hasData),
        ),
        child: Stack(
          children: [
            // Completion indicator background
            if (hasData && completionPercentage > 0)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      stops: [
                        0.0,
                        completionPercentage,
                        completionPercentage,
                        1.0,
                      ],
                      colors: [
                        isCompleted
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.orange.withValues(alpha: 0.3),
                        isCompleted
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.orange.withValues(alpha: 0.3),
                        Colors.transparent,
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            // Day number
            Center(
              child: Text(
                date.day.toString(),
                style: GoogleFonts.dmSans(
                  color: _getDayTextColor(isSelected, isToday, hasData),
                  fontWeight: isSelected || isToday
                      ? FontWeight.bold
                      : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
            ),
            // Success indicator dot
            if (isCompleted)
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getDayBackgroundColor(
    bool isSelected,
    bool isToday,
    bool hasData,
    bool isCompleted,
  ) {
    if (isSelected) {
      return widget.accentColor;
    }
    if (isToday) {
      return widget.accentColor.withValues(alpha: 0.2);
    }
    if (hasData) {
      return Colors.white.withValues(alpha: 0.05);
    }
    return Colors.transparent;
  }

  Border? _getDayBorder(bool isSelected, bool isToday, bool hasData) {
    if (isToday && !isSelected) {
      return Border.all(color: widget.accentColor, width: 1);
    }
    return null;
  }

  Color _getDayTextColor(bool isSelected, bool isToday, bool hasData) {
    if (isSelected) {
      return Colors.white;
    }
    if (hasData) {
      return Colors.white;
    }
    return Colors.white.withValues(alpha: 0.7);
  }

  FastingDay? _getFastingDataForDate(DateTime date) {
    try {
      return widget.fastingData.firstWhere(
        (fast) => _isSameDay(fast.date, date),
      );
    } catch (e) {
      return null;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }
}
