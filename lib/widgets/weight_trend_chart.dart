import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;

class WeightTrendChart extends StatefulWidget {
  final List<FlSpot> data;
  final double minY;
  final double maxY;

  const WeightTrendChart({
    Key? key,
    required this.data,
    required this.minY,
    required this.maxY,
  }) : super(key: key);

  @override
  State<WeightTrendChart> createState() => _WeightTrendChartState();
}

class _WeightTrendChartState extends State<WeightTrendChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _formatDate(double timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt());
    return DateFormat('MMM d').format(date);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) return const SizedBox.shrink();

    // Calculate Y-axis range with some padding
    final minWeight = widget.data.map((e) => e.y).reduce(math.min);
    final maxWeight = widget.data.map((e) => e.y).reduce(math.max);
    final yPadding = (maxWeight - minWeight) * 0.1;
    final effectiveMinY = math.max(widget.minY, minWeight - yPadding);
    final effectiveMaxY = math.min(widget.maxY, maxWeight + yPadding);

    // Calculate Y-axis intervals
    final yRange = effectiveMaxY - effectiveMinY;
    final yInterval = yRange <= 5
        ? 0.5
        : yRange <= 10
            ? 1.0
            : 2.0;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, _) {
        return Container(
          height: 300,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1D1E33),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Weight Trend',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: LineChart(
                  LineChartData(
                    lineTouchData: LineTouchData(
                      touchTooltipData: LineTouchTooltipData(
                        getTooltipItems: (touchedSpots) {
                          return touchedSpots.map((spot) {
                            final date = DateTime.fromMillisecondsSinceEpoch(
                                spot.x.toInt());
                            return LineTooltipItem(
                              '${DateFormat('MMM d, yyyy').format(date)}\n${spot.y.toStringAsFixed(1)} kg',
                              const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          }).toList();
                        },
                      ),
                      handleBuiltInTouches: true,
                      getTouchLineStart: (data, index) => 0,
                      getTouchLineEnd: (data, index) => double.infinity,
                    ),
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: false,
                      horizontalInterval: yInterval,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: Colors.white.withOpacity(0.1),
                          strokeWidth: 1,
                        );
                      },
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          interval: widget.data.length > 1
                              ? (widget.data.last.x - widget.data.first.x)
                              : 1,
                          getTitlesWidget: (value, meta) {
                            // Only show min and max x values
                            if (value == widget.data.first.x ||
                                value == widget.data.last.x) {
                              return Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  _formatDate(value),
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.5),
                                    fontSize: 12,
                                  ),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          interval: (widget.data.map((e) => e.y).reduce(math.max) -
                              widget.data.map((e) => e.y).reduce(math.min)),
                          getTitlesWidget: (value, meta) {
                            // Only show min and max y values
                            final minY = widget.data.map((e) => e.y).reduce(math.min);
                            final maxY = widget.data.map((e) => e.y).reduce(math.max);
                            if (value == minY || value == maxY) {
                              return Text(
                                value.toStringAsFixed(1),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.5),
                                  fontSize: 12,
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                          reservedSize: 40,
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    minX: widget.data.first.x,
                    maxX: widget.data.last.x,
                    minY: widget.data.map((e) => e.y).reduce(math.min),
                    maxY: widget.data.map((e) => e.y).reduce(math.max),
                    lineBarsData: [
                      LineChartBarData(
                        spots: widget.data.map((spot) {
                          return FlSpot(
                            spot.x,
                            spot.y * _animation.value,
                          );
                        }).toList(),
                        isCurved: true,
                        curveSmoothness: 0.35,
                        gradient: const LinearGradient(
                          colors: [
                            Color(0xFF448AFF),
                            Color(0xFF448AFF),
                          ],
                        ),
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: FlDotData(
                          show: widget.data.length <= 15, // Only show dots if data is not too large
                          getDotPainter: (spot, percent, barData, index) {
                            return FlDotCirclePainter(
                              radius: 6,
                              color: const Color(0xFF448AFF),
                              strokeWidth: 2,
                              strokeColor: Colors.white,
                            );
                          },
                        ),
                        // Only show min and max for x and y axes
                        showingIndicators: [0, widget.data.length - 1],
                      ),
                    ],
                  ),
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
