import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/fasting/fasting_bloc.dart';
import '../blocs/fasting/fasting_event.dart';
import '../blocs/fasting/fasting_state.dart';

/// Example showing how to use FastingBloc in your screens
/// This replaces direct FastingService usage with BLoC pattern
class FastingBlocUsageExample extends StatelessWidget {
  const FastingBlocUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Fasting BLoC Example')),
      body: BlocConsumer<FastingBloc, FastingState>(
        listener: (context, state) {
          // Handle errors
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.error!)),
            );
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Fast Status
                _buildCurrentFastCard(context, state),
                const SizedBox(height: 16),
                
                // Fast Controls
                _buildFastControls(context, state),
                const SizedBox(height: 16),
                
                // Daily Routine
                _buildDailyRoutine(context, state),
                const SizedBox(height: 16),
                
                // Statistics
                _buildStatistics(context, state),
                const SizedBox(height: 16),
                
                // Recent History
                _buildRecentHistory(context, state),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentFastCard(BuildContext context, FastingState state) {
    final currentFast = state.currentFastingDay;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Fast',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            if (currentFast == null)
              const Text('No active fast')
            else ...[
              Text('Status: ${currentFast.isActive ? "Active" : "Completed"}'),
              Text('Protocol: ${currentFast.fastingProtocol}'),
              if (currentFast.startDate != null)
                Text('Started: ${currentFast.startDate}'),
              if (currentFast.endDate != null)
                Text('Ended: ${currentFast.endDate}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFastControls(BuildContext context, FastingState state) {
    final isActive = state.currentFastingDay?.isActive ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fast Controls',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: isActive ? null : () => _startFast(context),
                    child: const Text('Start Fast'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isActive ? () => _endFast(context) : null,
                    child: const Text('End Fast'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyRoutine(BuildContext context, FastingState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Routine',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateWater(context),
                    child: const Text('Add Water'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateSteps(context),
                    child: const Text('Add Steps'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _updateExercise(context),
                    child: const Text('Add Exercise'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics(BuildContext context, FastingState state) {
    final stats = state.stats;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            if (stats == null)
              const Text('Loading statistics...')
            else ...[
              Text('Total Fasts: ${stats.totalFasts}'),
              Text('Completed: ${stats.completedFasts}'),
              Text('Current Streak: ${stats.currentStreak}'),
              Text('Longest Streak: ${stats.longestStreak}'),
              Text('Average Hours: ${stats.averageFastingHours.toStringAsFixed(1)}'),
              Text('Total Hours: ${stats.totalFastingHours.toStringAsFixed(1)}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecentHistory(BuildContext context, FastingState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent History',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                TextButton(
                  onPressed: () => _loadHistory(context),
                  child: const Text('Refresh'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (state.isLoadingHistory)
              const Center(child: CircularProgressIndicator())
            else if (state.recentHistory.isEmpty)
              const Text('No fasting history')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.recentHistory.length,
                itemBuilder: (context, index) {
                  final fast = state.recentHistory[index];
                  return ListTile(
                    title: Text('${fast.fastingProtocol} Fast'),
                    subtitle: Text(
                      '${fast.date.toString().split(' ')[0]} - '
                      '${fast.completed ? "Completed" : "Incomplete"}',
                    ),
                    trailing: fast.actualFastingMinutes != null
                        ? Text('${(fast.actualFastingMinutes! / 60).toStringAsFixed(1)}h')
                        : null,
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  // Action methods showing how to dispatch events
  void _startFast(BuildContext context) {
    context.read<FastingBloc>().add(
      StartFast(
        startDate: DateTime.now(),
        fastingProtocol: '16:8', // You can make this configurable
      ),
    );
  }

  void _endFast(BuildContext context) {
    context.read<FastingBloc>().add(
      EndFast(endDate: DateTime.now()),
    );
  }

  void _updateWater(BuildContext context) {
    context.read<FastingBloc>().add(
      UpdateDailyRoutine(
        date: DateTime.now(),
        waterIntake: 250.0, // 250ml
      ),
    );
  }

  void _updateSteps(BuildContext context) {
    context.read<FastingBloc>().add(
      UpdateDailyRoutine(
        date: DateTime.now(),
        steps: 1000,
      ),
    );
  }

  void _updateExercise(BuildContext context) {
    context.read<FastingBloc>().add(
      UpdateDailyRoutine(
        date: DateTime.now(),
        exercise: 30.0, // 30 minutes
      ),
    );
  }

  void _loadHistory(BuildContext context) {
    context.read<FastingBloc>().add(
      const LoadFastingHistory(refresh: true),
    );
  }
}

/// Example of how to listen to fasting progress in real-time
class FastingProgressListener extends StatelessWidget {
  const FastingProgressListener({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<FastingBloc, FastingState>(
      listenWhen: (previous, current) {
        // Only listen when there's an active fast and time updates
        return current.currentFastingDay?.isActive == true &&
               current.lastUpdated != previous.lastUpdated;
      },
      listener: (context, state) {
        // Handle real-time progress updates
        // This is called every minute when a fast is active
        print('Fast progress updated: ${state.lastUpdated}');
        
        // You can update UI, show notifications, etc.
      },
      child: const SizedBox.shrink(),
    );
  }
}
