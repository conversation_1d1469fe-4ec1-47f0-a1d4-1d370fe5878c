import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui';
import 'package:google_fonts/google_fonts.dart';
import '../models/achievement.dart';
import '../route/router_constants.dart';
import '../widgets/bottom_nav.dart';
import '../services/achievement_service.dart';

class AchievementsPage extends StatefulWidget {
  const AchievementsPage({super.key});

  @override
  State<AchievementsPage> createState() => _AchievementsPageState();
}

class _AchievementsPageState extends State<AchievementsPage>
    with TickerProviderStateMixin, RestorationMixin {
  late AnimationController _fadeController;
  late AchievementService _achievementService;

  // Restoration properties
  final RestorableInt _selectedTabIndex = RestorableInt(0);

  // Local state
  List<Achievement> _achievements = [];
  List<Achievement> _lockedAchievements = [];
  bool _isLoading = true;
  String? _error;

  @override
  String? get restorationId => 'achievements_page';

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    registerForRestoration(_selectedTabIndex, 'selected_tab_index');
  }

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..forward();

    _initializeAchievements();
  }

  Future<void> _initializeAchievements() async {
    try {
      _achievementService = AchievementService();
      await _achievementService.init();
      await _loadAchievements();
      await _checkForNewAchievements();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize achievements: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAchievements() async {
    try {
      final achievements = _achievementService.getAllAchievements();
      final allPossibleAchievements = _achievementService
          .getAllPossibleAchievements();
      final unlockedIds = achievements.map((a) => a.id).toSet();
      final lockedAchievements = allPossibleAchievements
          .where((a) => !unlockedIds.contains(a.id))
          .toList();

      setState(() {
        _achievements = achievements;
        _lockedAchievements = lockedAchievements;
        _isLoading = false;
        _error = null;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load achievements: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _checkForNewAchievements() async {
    try {
      // For now, pass an empty list - this would normally come from FastingService
      await _achievementService.checkForNewAchievements([]);
      // Reload achievements to get any newly unlocked ones
      await _loadAchievements();
    } catch (e) {
      // Silently handle errors for background checks
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _selectedTabIndex.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show error if any
    if (_error != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_error!), backgroundColor: Colors.red),
        );
        setState(() {
          _error = null;
        });
      });
    }

    // Show loading state
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFF0A0E21),
        body: Center(child: CircularProgressIndicator(color: Colors.white)),
      );
    }

    final totalPoints = _achievementService.getTotalPoints();

    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildAppBar(totalPoints),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPointsCard(totalPoints),
                    const SizedBox(height: 24),
                    Text(
                      'Your Achievements',
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            _buildAchievementsList(_achievements, _lockedAchievements),
          ],
        ),
      ),
      bottomNavigationBar: BottomNav(currentRoute: RouteConstants.achievements),
    );
  }

  Widget _buildAppBar(int points) {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Achievements',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
        centerTitle: false,
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF0A0E21),
                const Color(0xFF0A0E21).withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white70),
          onPressed: () => GoRouter.of(context).goNamed(RouteConstants.home),
        ),
      ),
    );
  }

  Widget _buildPointsCard(int points) {
    return FadeTransition(
      opacity: _fadeController,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.emoji_events,
                      color: Colors.amber,
                      size: 32,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Points',
                      style: GoogleFonts.dmSans(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '$points',
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAchievementsList(
    List<Achievement> unlockedAchievements,
    List<Achievement> lockedAchievements,
  ) {
    if (unlockedAchievements.isEmpty && lockedAchievements.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.emoji_events_outlined,
                size: 64,
                color: Colors.grey.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No achievements yet',
                style: GoogleFonts.dmSans(color: Colors.grey, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                'Complete fasting sessions to earn achievements',
                style: GoogleFonts.dmSans(
                  color: Colors.grey.withValues(alpha: 0.7),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == 0 && unlockedAchievements.isNotEmpty) {
            return Padding(
              padding: const EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                bottom: 8.0,
              ),
              child: Text(
                'Unlocked (${unlockedAchievements.length})',
                style: GoogleFonts.dmSans(
                  color: Colors.green,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }

          if (index <= unlockedAchievements.length) {
            if (index == 0) return const SizedBox.shrink();
            final achievement = unlockedAchievements[index - 1];
            return _buildAchievementCard(achievement, true);
          }

          if (index == unlockedAchievements.length + 1 &&
              lockedAchievements.isNotEmpty) {
            return Padding(
              padding: const EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: 16.0,
                bottom: 8.0,
              ),
              child: Text(
                'Locked (${lockedAchievements.length})',
                style: GoogleFonts.dmSans(
                  color: Colors.grey,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }

          final lockedIndex = index - unlockedAchievements.length - 2;
          if (lockedIndex >= 0 && lockedIndex < lockedAchievements.length) {
            final achievement = lockedAchievements[lockedIndex];
            return _buildAchievementCard(achievement, false);
          }

          return const SizedBox.shrink();
        },
        childCount: 2 + unlockedAchievements.length + lockedAchievements.length,
      ),
    );
  }

  Widget _buildAchievementCard(Achievement achievement, bool isUnlocked) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: isUnlocked
                  ? Colors.white.withValues(alpha: 0.08)
                  : Colors.white.withValues(alpha: 0.03),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isUnlocked
                    ? Colors.white.withValues(alpha: 0.15)
                    : Colors.white.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              leading: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isUnlocked
                      ? Colors.blue.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    _getIconData(achievement.iconName),
                    color: isUnlocked ? Colors.amber : Colors.grey,
                    size: 28,
                  ),
                ),
              ),
              title: Text(
                achievement.title,
                style: GoogleFonts.dmSans(
                  color: isUnlocked
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    achievement.description,
                    style: GoogleFonts.dmSans(
                      color: isUnlocked ? Colors.white70 : Colors.white38,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: isUnlocked ? Colors.amber : Colors.grey,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${achievement.points} points',
                        style: GoogleFonts.dmSans(
                          color: isUnlocked ? Colors.amber : Colors.grey,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              trailing: isUnlocked
                  ? Text(
                      _formatDate(achievement.unlockedDate),
                      style: GoogleFonts.dmSans(
                        color: Colors.white54,
                        fontSize: 12,
                      ),
                    )
                  : Icon(
                      Icons.lock,
                      color: Colors.grey.withValues(alpha: 0.5),
                      size: 20,
                    ),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'trophy':
        return Icons.emoji_events;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'whatshot':
        return Icons.whatshot;
      case 'timer':
        return Icons.timer;
      default:
        return Icons.emoji_events;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
