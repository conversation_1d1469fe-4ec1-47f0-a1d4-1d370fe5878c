import 'package:fasttime/route/router_constants.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fasttime/widgets/bottom_nav.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // App Bar
            SliverAppBar(
              backgroundColor: Colors.transparent,
              floating: true,
              pinned: true,
              title: Text(
                'Terms of Service',
                style: GoogleFonts.dmMono(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            // Content
            SliverPadding(
              padding: const EdgeInsets.all(20.0),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildSection(
                    'Agreement to Terms',
                    'By accessing and using FastTime, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using this app.',
                  ),
                  _buildSection(
                    'Use License',
                    'Permission is granted to use FastTime for personal, non-commercial purposes, subject to the following restrictions:\n\n'
                        '• You may not modify or copy the app\n'
                        '• You may not use the app for commercial purposes\n'
                        '• You may not attempt to reverse engineer the app\n'
                        '• You may not remove any copyright or proprietary notices',
                  ),
                  _buildSection(
                    'Disclaimer',
                    'FastTime is provided "as is" without any warranties, expressed or implied. We do not warrant that:\n\n'
                        '• The app will function uninterrupted\n'
                        '• The app is error-free\n'
                        '• The app is suitable for your specific needs\n'
                        '• The app will meet your requirements',
                  ),
                  _buildSection(
                    'Health Disclaimer',
                    'FastTime is not a medical app and should not be used as a substitute for professional medical advice:\n\n'
                        '• Consult healthcare professionals before starting any fasting regimen\n'
                        '• The app does not provide medical advice\n'
                        '• Use the app at your own risk\n'
                        '• We are not responsible for any health-related decisions made using the app',
                  ),
                  _buildSection(
                    'Limitations',
                    'In no event shall FastTime or its developers be liable for any damages arising out of the use or inability to use the app, including but not limited to:\n\n'
                        '• Direct, indirect, or consequential damages\n'
                        '• Loss of data or profit\n'
                        '• Business interruption\n'
                        '• Personal injury or health issues',
                  ),
                  _buildSection(
                    'Accuracy of Information',
                    'While we strive to provide accurate information about fasting and health, we make no guarantees about the completeness, reliability, or accuracy of the information in the app.',
                  ),
                  _buildSection(
                    'User Responsibilities',
                    'As a user of FastTime, you agree to:\n\n'
                        '• Provide accurate information\n'
                        '• Use the app responsibly\n'
                        '• Not misuse or abuse the app\n'
                        '• Not attempt to harm the app or its users',
                  ),
                  _buildSection(
                    'Termination',
                    'We reserve the right to terminate or suspend access to FastTime for any user who violates these Terms of Service.',
                  ),
                  _buildSection(
                    'Changes to Terms',
                    'We reserve the right to modify these terms at any time. We will notify users of any material changes to these terms.',
                  ),
                  _buildSection(
                    'Governing Law',
                    'These terms shall be governed by and construed in accordance with the laws of the jurisdiction in which the app is operated, without regard to its conflict of law provisions.',
                  ),
                  _buildSection(
                    'Contact Information',
                    'If you have any questions about these Terms of Service, please contact us at:\n\n'
                        '<EMAIL>',
                  ),
                  const SizedBox(height: 40),
                ]),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const BottomNav(
        currentRoute: RouteConstants.settings,
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.dmMono(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.dmMono(
              color: Colors.white70,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
