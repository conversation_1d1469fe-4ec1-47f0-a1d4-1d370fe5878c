import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fasttime/widgets/bottom_nav.dart';
import 'package:fasttime/route/router_constants.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      body: Safe<PERSON>rea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // App Bar
            SliverAppBar(
              backgroundColor: Colors.transparent,
              floating: true,
              pinned: true,
              title: Text(
                'Privacy Policy',
                style: GoogleFonts.dmMono(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            // Content
            SliverPadding(
              padding: const EdgeInsets.all(20.0),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildSection(
                    'Introduction',
                    'FastTime is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your information when you use our mobile application.',
                  ),
                  _buildSection(
                    'Information We Collect',
                    'We collect minimal information necessary for the app\'s functionality:\n\n'
                        '• Fasting data (start/end times, protocols)\n'
                        '• Daily habits (water intake, steps, exercise)\n'
                        '• App preferences and settings\n\n'
                        'All data is stored locally on your device unless you explicitly choose to sync with cloud services.',
                  ),
                  _buildSection(
                    'How We Use Your Information',
                    'Your information is used solely to:\n\n'
                        '• Provide fasting tracking functionality\n'
                        '• Display your progress and statistics\n'
                        '• Improve app performance and user experience\n'
                        '• Send notifications (if enabled)',
                  ),
                  _buildSection(
                    'Data Storage',
                    '• All data is stored locally on your device\n'
                        '• Optional cloud backup (if enabled)\n'
                        '• You can export or delete your data at any time',
                  ),
                  _buildSection(
                    'Third-Party Services',
                    'FastTime may use third-party services that collect information:\n\n'
                        '• Analytics services to improve app performance\n'
                        '• Cloud services for optional data backup\n'
                        '• These services have their own privacy policies',
                  ),
                  _buildSection(
                    'Your Rights',
                    'You have the right to:\n\n'
                        '• Access your personal data\n'
                        '• Export your data\n'
                        '• Delete your data\n'
                        '• Opt-out of analytics\n'
                        '• Disable notifications',
                  ),
                  _buildSection(
                    'Children\'s Privacy',
                    'FastTime is not intended for use by children under 13. We do not knowingly collect personal information from children under 13.',
                  ),
                  _buildSection(
                    'Changes to Privacy Policy',
                    'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.',
                  ),
                  _buildSection(
                    'Contact Us',
                    'If you have any questions about this Privacy Policy, please contact us at:\n\n'
                        '<EMAIL>',
                  ),
                  const SizedBox(height: 40),
                ]),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const BottomNav(
        currentRoute: RouteConstants.settings,
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.dmMono(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.dmMono(
              color: Colors.white70,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
