import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui';
import 'package:google_fonts/google_fonts.dart';
import '../models/fasting_day.dart';
import '../route/router_constants.dart';
import '../widgets/bottom_nav.dart';
import '../widgets/fasting_calendar.dart';
import '../services/fasting_service.dart';
import '../services/achievement_service.dart';
import '../models/achievement.dart';

class ProgressPage extends StatefulWidget {
  const ProgressPage({super.key});

  @override
  State<ProgressPage> createState() => _ProgressPageState();
}

class _ProgressPageState extends State<ProgressPage> {
  DateTime _selectedDate = DateTime.now();
  late FastingService _fastingService;
  late AchievementService _achievementService;
  List<FastingDay> _completedFasts = [];
  List<Achievement> _lockedAchievements = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeFastingService();
  }

  Future<void> _initializeFastingService() async {
    try {
      _fastingService = FastingService();
      await _fastingService.init();

      _achievementService = AchievementService();
      await _achievementService.init();

      await _loadCompletedFasts();
      await _loadLockedAchievements();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize services: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCompletedFasts() async {
    try {
      final fasts = _fastingService.getCompletedFasts();
      setState(() {
        _completedFasts = fasts;
        _isLoading = false;
        _error = null;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load completed fasts: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadLockedAchievements() async {
    try {
      final achievements = _achievementService.getAllAchievements();
      final allPossibleAchievements = _achievementService
          .getAllPossibleAchievements();
      final unlockedIds = achievements.map((a) => a.id).toSet();
      final lockedAchievements = allPossibleAchievements
          .where((a) => !unlockedIds.contains(a.id))
          .toList();

      setState(() {
        _lockedAchievements = lockedAchievements;
      });
    } catch (e) {
      // Silently handle errors for achievements loading
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show error if any
    if (_error != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_error!), backgroundColor: Colors.red),
        );
        setState(() {
          _error = null;
        });
      });
    }

    // Show loading state
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFF0A0E21),
        body: Center(child: CircularProgressIndicator(color: Colors.white)),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildAppBar(),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatsCard(_completedFasts),
                      const SizedBox(height: 24),
                      Text(
                        'Fasting Calendar',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildCalendarView(),
                      const SizedBox(height: 24),
                      Text(
                        'Daily Details',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDailyDetails(_completedFasts, _selectedDate),
                      const SizedBox(height: 24),
                      Text(
                        'Upcoming Achievements',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildUpcomingAchievements(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNav(currentRoute: RouteConstants.progress),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Progress',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
        centerTitle: false,
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF0A0E21),
                const Color(0xFF0A0E21).withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white70),
          onPressed: () => GoRouter.of(context).goNamed(RouteConstants.home),
        ),
      ),
    );
  }

  Widget _buildStatsCard(List<FastingDay> completedFasts) {
    final totalFasts = completedFasts.length;
    final successfulFasts = completedFasts.where((fast) {
      final targetHours = int.parse(fast.fastingProtocol.split(':')[0]);
      final actualMinutes = fast.actualFastingMinutes ?? 0;
      return actualMinutes >= targetHours * 60;
    }).length;

    final successRate = totalFasts > 0
        ? (successfulFasts / totalFasts * 100).round()
        : 0;

    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('Total Fasts', '$totalFasts'),
                  _buildStatItem('Success Rate', '$successRate%'),
                  _buildStatItem(
                    'Streak',
                    _calculateCurrentStreak(completedFasts).toString(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 12),
        ),
      ],
    );
  }

  int _calculateCurrentStreak(List<FastingDay> fasts) {
    if (fasts.isEmpty) return 0;

    // Sort by date descending
    final sortedFasts = List<FastingDay>.from(fasts)
      ..sort((a, b) => b.date.compareTo(a.date));

    int streak = 1;
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);

    // Check if the most recent fast is from today or yesterday
    final mostRecentFastDate = DateTime(
      sortedFasts[0].date.year,
      sortedFasts[0].date.month,
      sortedFasts[0].date.day,
    );

    if (todayDate.difference(mostRecentFastDate).inDays > 1) {
      return 0; // Streak broken
    }

    // Count consecutive days
    for (int i = 0; i < sortedFasts.length - 1; i++) {
      final currentDate = DateTime(
        sortedFasts[i].date.year,
        sortedFasts[i].date.month,
        sortedFasts[i].date.day,
      );

      final nextDate = DateTime(
        sortedFasts[i + 1].date.year,
        sortedFasts[i + 1].date.month,
        sortedFasts[i + 1].date.day,
      );

      if (currentDate.difference(nextDate).inDays == 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  Widget _buildDailyDetails(List<FastingDay> fasts, DateTime selectedDate) {
    final selectedDateNormalized = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
    );

    final fastForSelectedDate = fasts.where((fast) {
      final fastDate = DateTime(fast.date.year, fast.date.month, fast.date.day);
      return fastDate.isAtSameMomentAs(selectedDateNormalized);
    }).toList();

    if (fastForSelectedDate.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Text(
            'No fasting data for this date',
            style: GoogleFonts.dmMono(color: Colors.white70, fontSize: 14),
          ),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: fastForSelectedDate.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final fast = fastForSelectedDate[index];
        final targetHours = int.parse(fast.fastingProtocol.split(':')[0]);
        final actualMinutes = fast.actualFastingMinutes ?? 0;
        final percentComplete = (actualMinutes / (targetHours * 60) * 100)
            .clamp(0, 100);
        return Dismissible(
          key: Key(fast.id),
          direction: DismissDirection.endToStart,
          background: Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            color: Colors.red,
            child: const Icon(Icons.delete, color: Colors.white),
          ),
          confirmDismiss: (direction) async {
            return await showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete Entry'),
                content: const Text(
                  'Are you sure you want to delete this fasting record?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            );
          },
          onDismissed: (direction) async {
            try {
              await _fastingService.deleteFastingDay(fast);
              await _loadCompletedFasts();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Fasting record deleted'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to delete record: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Protocol: ${fast.fastingProtocol}',
                  style: GoogleFonts.dmMono(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildDetailItem('Target', '${targetHours}h'),
                    _buildDetailItem(
                      'Actual',
                      '${(actualMinutes / 60).toStringAsFixed(1)}h',
                    ),
                    _buildDetailItem(
                      'Completion',
                      '${percentComplete.round()}%',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: percentComplete / 100,
                  backgroundColor: Colors.white.withValues(alpha: 0.1),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    percentComplete >= 100 ? Colors.green : Colors.orange,
                  ),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.dmMono(color: Colors.white70, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.dmMono(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarView() {
    return FastingCalendar(
      fastingData: _completedFasts,
      selectedDate: _selectedDate,
      onDateSelected: (DateTime date) {
        setState(() {
          _selectedDate = date;
        });
      },
      accentColor: const Color(0xFF6B4EFF),
      backgroundColor: const Color(0xFF191919),
    );
  }

  Widget _buildUpcomingAchievements() {
    if (_lockedAchievements.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            'All achievements unlocked! 🎉',
            style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 14),
          ),
        ),
      );
    }

    // Show only the first 3 locked achievements
    final displayAchievements = _lockedAchievements.take(3).toList();

    return Column(
      children: [
        ...displayAchievements.map(
          (achievement) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildLockedAchievementCard(achievement),
          ),
        ),
        if (_lockedAchievements.length > 3)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                '+${_lockedAchievements.length - 3} more achievements to unlock',
                style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 12),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLockedAchievementCard(Achievement achievement) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getIconData(achievement.iconName),
              color: Colors.grey.withValues(alpha: 0.6),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: GoogleFonts.dmSans(
                    color: Colors.white70,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: GoogleFonts.dmSans(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${achievement.points} pts',
              style: GoogleFonts.dmSans(
                color: Colors.orange,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'trophy':
        return Icons.emoji_events;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'whatshot':
        return Icons.whatshot;
      case 'timer':
        return Icons.timer;
      case 'repeat':
        return Icons.repeat;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'nights_stay':
        return Icons.nights_stay;
      case 'star':
        return Icons.star;
      case 'diamond':
        return Icons.diamond;
      case 'military_tech':
        return Icons.military_tech;
      default:
        return Icons.emoji_events;
    }
  }
}
