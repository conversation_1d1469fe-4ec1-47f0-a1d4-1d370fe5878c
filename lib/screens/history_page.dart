import 'package:flutter/material.dart';
import 'package:fasttime/models/fasting_day.dart';
import 'package:fasttime/widgets/bottom_nav.dart';
import 'package:fasttime/services/fasting_service.dart';
import 'package:fasttime/route/router_constants.dart';
import 'package:intl/intl.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage>
    with TickerProviderStateMixin {
  late AnimationController _cardAnimationController;
  late AnimationController _shimmerController;
  late FastingService _fastingService;
  List<FastingDay> _completedFasts = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeFastingService();

    _cardAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _shimmerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  Future<void> _initializeFastingService() async {
    try {
      _fastingService = FastingService();
      await _fastingService.init();
      await _loadData();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize fasting service: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final fasts = _fastingService.getCompletedFasts();
      setState(() {
        _completedFasts = fasts;
        _isLoading = false;
        _cardAnimationController.forward();
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load completed fasts: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _cardAnimationController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  String _formatDuration(FastingDay day) {
    try {
      final startParts = day.startTime.split(':');
      final endParts = day.endTime.split(':');

      final startTime = DateTime(
        day.date.year,
        day.date.month,
        day.date.day,
        int.parse(startParts[0]),
        int.parse(startParts[1]),
      );

      var endTime = DateTime(
        day.date.year,
        day.date.month,
        day.date.day,
        int.parse(endParts[0]),
        int.parse(endParts[1]),
      );

      if (endTime.isBefore(startTime)) {
        endTime = endTime.add(const Duration(days: 1));
      }

      final duration = endTime.difference(startTime);
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } catch (e) {
      return '--';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));

    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return 'Today';
    }

    if (date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day) {
      return 'Yesterday';
    }

    return DateFormat('d MMM yyyy').format(date); // e.g., "15 Jan 2024"
  }

  @override
  Widget build(BuildContext context) {
    // Show error if any
    if (_error != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_error!), backgroundColor: Colors.red),
        );
        setState(() {
          _error = null;
        });
      });
    }

    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            SliverAppBar(
              floating: true,
              backgroundColor: const Color(0xFF121212),
              expandedHeight: MediaQuery.of(context).size.height * 0.1,
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                title: const Text(
                  'Fasting History',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFF1A1A1A),
                        const Color(0xFF121212),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              sliver: _isLoading
                  ? SliverFillRemaining(
                      child: Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      ),
                    )
                  : _completedFasts.isEmpty
                  ? SliverFillRemaining(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.history,
                              size: 64,
                              color: Colors.white.withValues(alpha: 0.2),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No completed fasts yet',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.6),
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        final day = _completedFasts[index];
                        return _buildFastingCard(day, index);
                      }, childCount: _completedFasts.length),
                    ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNav(currentRoute: RouteConstants.progress),
    );
  }

  Widget _buildFastingCard(FastingDay day, int index) {
    final animation = CurvedAnimation(
      parent: _cardAnimationController,
      curve: Interval(
        (index / 20).clamp(0.0, 1.0), // Stagger animation for up to 20 items
        1.0,
        curve: Curves.easeOutQuart,
      ),
    );

    final isOvertime = _calculateOvertime(day);

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 0.1),
        end: Offset.zero,
      ).animate(animation),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1E1E1E),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Shimmer effect
                AnimatedBuilder(
                  animation: _shimmerController,
                  builder: (context, child) {
                    return Positioned(
                      left: -100 + (_shimmerController.value * 400),
                      top: 0,
                      bottom: 0,
                      width: 200,
                      child: Transform.rotate(
                        angle: 0.4,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0),
                                Colors.white.withValues(alpha: 0.05),
                                Colors.white.withValues(alpha: 0),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _formatDate(day.date),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                DateFormat('EEEE').format(day.date),
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.7),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: isOvertime
                                  ? Colors.green.withValues(alpha: 0.2)
                                  : Colors.blue.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  isOvertime ? Icons.star : Icons.check_circle,
                                  color: isOvertime
                                      ? Colors.green
                                      : Colors.blue,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _formatDuration(day),
                                  style: TextStyle(
                                    color: isOvertime
                                        ? Colors.green
                                        : Colors.blue,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildTimelineRow(day),
                      const SizedBox(height: 16),
                      _buildMetricsRow(day),
                      if (day.achievements.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        _buildAchievementsRow(day),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _calculateOvertime(FastingDay day) {
    try {
      final startParts = day.startTime.split(':');
      final endParts = day.endTime.split(':');

      final startTime = DateTime(
        day.date.year,
        day.date.month,
        day.date.day,
        int.parse(startParts[0]),
        int.parse(startParts[1]),
      );

      var endTime = DateTime(
        day.date.year,
        day.date.month,
        day.date.day,
        int.parse(endParts[0]),
        int.parse(endParts[1]),
      );

      if (endTime.isBefore(startTime)) {
        endTime = endTime.add(const Duration(days: 1));
      }

      final duration = endTime.difference(startTime);
      final targetDuration = Duration(
        hours: int.parse(day.fastingProtocol.split(':')[0]),
      );

      return duration > targetDuration;
    } catch (e) {
      return false;
    }
  }

  Widget _buildTimelineRow(FastingDay day) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildTimelinePoint(
            time: day.startTime,
            label: 'Started',
            icon: Icons.wb_sunny_outlined,
            color: Colors.orange,
          ),
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  height: 2,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
                Icon(
                  Icons.arrow_forward,
                  color: Colors.white.withValues(alpha: 0.3),
                  size: 16,
                ),
              ],
            ),
          ),
          _buildTimelinePoint(
            time: day.endTime,
            label: 'Ended',
            icon: Icons.nightlight_outlined,
            color: Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelinePoint({
    required String time,
    required String label,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(height: 4),
        Text(
          time,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricsRow(FastingDay day) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildMetricItem(Icons.water_drop, '${day.waterIntake}L', Colors.blue),
        _buildMetricItem(Icons.directions_walk, '${day.steps}', Colors.green),
        _buildMetricItem(
          Icons.directions_run,
          '${day.exercise}h',
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildMetricItem(IconData icon, String value, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementsRow(FastingDay day) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: day.achievements.map((achievement) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.emoji_events, color: Colors.purple, size: 16),
              const SizedBox(width: 4),
              Text(
                achievement,
                style: const TextStyle(
                  color: Colors.purple,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
