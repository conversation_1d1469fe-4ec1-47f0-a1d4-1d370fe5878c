import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0A0E21),
        title: Text(
          'Privacy Policy',
          style: GoogleFonts.dmMono(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => GoRouter.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Privacy Policy',
                style: GoogleFonts.dmMono(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Last updated: ${DateTime.now().year}',
                style: GoogleFonts.dmSans(fontSize: 14, color: Colors.white70),
              ),
              const SizedBox(height: 24),

              _buildSection(
                'Information We Collect',
                'We collect information you provide directly to us, such as when you create an account, log your fasting sessions, or contact us for support. This may include your email address, fasting data, and app usage information.',
              ),

              _buildSection(
                'How We Use Your Information',
                'We use the information we collect to provide, maintain, and improve our services, including tracking your fasting progress, sending notifications, and providing customer support.',
              ),

              _buildSection(
                'Data Storage and Security',
                'Your data is stored securely using industry-standard encryption. We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
              ),

              _buildSection(
                'Information Sharing',
                'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law.',
              ),

              _buildSection(
                'Local Data Storage',
                'Most of your fasting data is stored locally on your device. This includes your fasting sessions, progress tracking, and app preferences. This data remains on your device and is not automatically shared with our servers.',
              ),

              _buildSection(
                'Analytics and Crash Reporting',
                'We may use third-party analytics services to help us understand how our app is used. These services may collect information such as app usage patterns and crash reports to help us improve the app.',
              ),

              _buildSection(
                'Your Rights',
                'You have the right to access, update, or delete your personal information. You can do this through the app settings or by contacting us directly.',
              ),

              _buildSection(
                'Data Retention',
                'We retain your information for as long as your account is active or as needed to provide you services. You may delete your account and associated data at any time through the app settings.',
              ),

              _buildSection(
                'Children\'s Privacy',
                'Our service is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.',
              ),

              _buildSection(
                'Changes to This Policy',
                'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy in the app and updating the "Last updated" date.',
              ),

              _buildSection(
                'Contact Us',
                'If you have any questions about this Privacy Policy, please contact us through the app\'s support feature.',
              ),

              const SizedBox(height: 32),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF1D1E33),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Privacy Matters',
                      style: GoogleFonts.dmSans(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF6B4EFF),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'We are committed to protecting your privacy and being transparent about how we handle your data. Most of your fasting data stays on your device, giving you full control over your personal information.',
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        color: Colors.white70,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: GoogleFonts.dmSans(
              fontSize: 14,
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
