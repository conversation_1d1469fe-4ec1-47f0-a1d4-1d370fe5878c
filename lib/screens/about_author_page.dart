import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutAuthorPage extends StatefulWidget {
  const AboutAuthorPage({super.key});

  @override
  State<AboutAuthorPage> createState() => _AboutAuthorPageState();
}

class _AboutAuthorPageState extends State<AboutAuthorPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildAppBar(),
            SliverFadeTransition(
              opacity: _fadeAnimation,
              sliver: SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildAuthorCard(),
                      const SizedBox(height: 24),
                      _buildAboutSection(),
                      const SizedBox(height: 24),
                      _buildProjectSection(),
                      const SizedBox(height: 24),
                      _buildSupportSection(),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: const Color(0xFF0A0E21),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => context.pop(),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'About Author',
          style: GoogleFonts.dmMono(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF6B4EFF).withOpacity(0.4),
                const Color(0xFF0A0E21).withOpacity(0.0),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAuthorCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6B4EFF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [const Color(0xFF6B4EFF), const Color(0xFF9C3EFF)],
              ),
            ),
            child: CircleAvatar(
              backgroundImage: NetworkImage(
                'https://avatars.githubusercontent.com/u/105870053?v=4', // Replace with your image URL
              ),
              radius: 50,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Allen Febi M A',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Flutter Developer | Full Stack Enthusiast',
            style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return _buildSection(
      title: 'About Me',
      child: Text(
        'Hi! I\'m Allen, a passionate Flutter developer who believes in the power of intermittent fasting for health and wellness. I created FastTime to help people track their fasting journey with a beautiful, intuitive interface.\n\n'
        'As someone who practices intermittent fasting myself, I understand the importance of having a reliable companion app that makes tracking effortless and motivating. FastTime is built with love and attention to detail, focusing on user experience and data privacy.\n\n'
        'When I\'m not coding, you can find me exploring new technologies, reading about health and nutrition, or enjoying time outdoors.',
        style: GoogleFonts.dmSans(
          color: Colors.white70,
          fontSize: 16,
          height: 1.6,
        ),
      ),
    );
  }

  Widget _buildProjectSection() {
    return _buildSection(
      title: 'About FastTime',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'FastTime is a modern, privacy-focused intermittent fasting tracker designed to help you achieve your health goals. Built with Flutter for a smooth cross-platform experience.',
            style: GoogleFonts.dmSans(
              color: Colors.white70,
              fontSize: 16,
              height: 1.6,
            ),
          ),
          const SizedBox(height: 16),
          _buildFeatureItem('🎯', 'Simple & Intuitive Design'),
          _buildFeatureItem('📊', 'Comprehensive Progress Tracking'),
          _buildFeatureItem('🏆', 'Achievement System'),
          _buildFeatureItem('🔒', 'Privacy-First Approach'),
          _buildFeatureItem('📱', 'Cross-Platform Support'),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 18)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return _buildSection(
      title: 'Support the Project',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'If you find FastTime helpful and would like to support its development, consider making a donation. Your support helps keep the app free and enables me to add new features and improvements.',
            style: GoogleFonts.dmSans(
              color: Colors.white70,
              fontSize: 16,
              height: 1.6,
            ),
          ),
          const SizedBox(height: 20),
          _buildDonationButtons(),
          const SizedBox(height: 20),
          _buildContactButtons(),
        ],
      ),
    );
  }

  Widget _buildDonationButtons() {
    return Column(
      children: [
        _buildDonationButton(
          'Buy me a coffee ☕',
          'Support with a small donation',
          const Color(0xFFFFDD44),
          () => _launchURL('https://coff.ee/all3n2601'),
        ),
        // const SizedBox(height: 12),
        // _buildDonationButton(
        //   'PayPal Donation 💳',
        //   'One-time or recurring donation',
        //   const Color(0xFF0070BA),
        //   () => _launchURL('https://paypal.me/allen'),
        // ),
        const SizedBox(height: 12),
        _buildDonationButton(
          'GitHub Sponsors 💖',
          'Support on GitHub',
          const Color(0xFFEA4AAA),
          () => _launchURL('https://github.com/sponsors/all3n2601'),
        ),
      ],
    );
  }

  Widget _buildContactButtons() {
    return Column(
      children: [
        _buildContactButton(
          'GitHub Profile',
          'Check out my other projects',
          Icons.code,
          () => _launchURL('https://github.com/all3n2601'),
        ),
        const SizedBox(height: 12),
        _buildContactButton(
          'Send Feedback',
          'Share your thoughts and suggestions',
          Icons.email_outlined,
          () =>
              _launchURL('mailto:<EMAIL>?subject=FastTime Feedback'),
        ),
      ],
    );
  }

  Widget _buildDonationButton(
    String title,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3), width: 1),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.favorite, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.dmSans(
                      color: Colors.white60,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildContactButton(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF1D1E33),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFF6B4EFF).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF6B4EFF).withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF6B4EFF), size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.dmSans(
                      color: Colors.white60,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF6B4EFF),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF1D1E33),
            borderRadius: BorderRadius.circular(16),
          ),
          child: child,
        ),
      ],
    );
  }

  Future<void> _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showErrorSnackBar('Could not open $url');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error opening link: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class SliverFadeTransition extends StatelessWidget {
  final Animation<double> opacity;
  final Widget sliver;

  const SliverFadeTransition({
    super.key,
    required this.opacity,
    required this.sliver,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: opacity,
      builder: (context, child) {
        return SliverOpacity(opacity: opacity.value, sliver: sliver);
      },
    );
  }
}
