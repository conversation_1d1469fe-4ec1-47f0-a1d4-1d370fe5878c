import 'package:fasttime/models/fasting_day.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../route/router_constants.dart';

Widget buildHeader({FastingDay? fastingDay,String? subtitle, required BuildContext context}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'FastTime',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 4),
          Text(
            subtitle ?? (fastingDay != null ? fastingDay.fastingProtocol : 'Welcome!'),
            style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 14),
          ),
        ],
      ),
      IconButton(
        icon: Icon(Icons.settings_outlined, color: Colors.white70),
        onPressed: () {
          GoRouter.of(context).pushNamed(RouteConstants.settings);
        },
      ),
    ],
  );
}