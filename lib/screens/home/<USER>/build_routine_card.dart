import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildRoutineCard(
    String title,
    IconData icon,
    Color color,
    String value,
    double progress,
    Function(double) onChanged,
    BuildContext context,
    {required Size screenSize}) {
  final padding = screenSize.width * 0.03; // 3% of screen width
  final iconSize = screenSize.width * 0.045; // 4.5% of screen width
  final fontSize = screenSize.width * 0.035; // 3.5% of screen width
  final buttonSize = screenSize.width * 0.06; // 6% of screen width
  final verticalSpacing = screenSize.height * 0.01; // 1% of screen height

  return Container(
    padding: EdgeInsets.all(padding),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.05),
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Icon(icon, color: color, size: iconSize),
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: fontSize,
              ),
            ),
          ],
        ),
        SizedBox(height: verticalSpacing * 1.2),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Color(0xFF444444),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
        SizedBox(height: verticalSpacing * 0.8),
        Text(
          value,
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: verticalSpacing * 0.8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () => onChanged((progress - 0.1).clamp(0.0, 1.0)),
              child: Container(
                width: buttonSize,
                height: buttonSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFF444444),
                ),
                child: Icon(
                  Icons.remove,
                  color: Colors.white,
                  size: buttonSize * 0.7,
                ),
              ),
            ),
            GestureDetector(
              onTap: () => onChanged((progress + 0.1).clamp(0.0, 1.0)),
              child: Container(
                width: buttonSize,
                height: buttonSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: color.withOpacity(0.8),
                ),
                child: Icon(
                  Icons.add,
                  color: Colors.white,
                  size: buttonSize * 0.7,
                ),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}