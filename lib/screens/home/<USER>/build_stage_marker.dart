import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildStageMarker(
    String hour,
    Color color,
    bool isActive,
    bool isCurrent,
    IconData icon,
    ) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isActive ? color : Color(0xFF444444),
          border: isCurrent
              ? Border.all(color: Colors.white, width: 2)
              : isActive
              ? Border.all(color: color.withOpacity(0.3), width: 1)
              : null,
          boxShadow: isCurrent
              ? [
            BoxShadow(
              color: color.withOpacity(0.5),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ]
              : null,
        ),
        child: Center(
          child: AnimatedSwitcher(
            duration: Duration(milliseconds: 300),
            child: isCurrent
                ? Icon(
              icon,
              key: ValueKey('current'),
              color: Colors.white,
              size: 20,
            )
                : isActive
                ? Icon(
              Icons.check,
              key: <PERSON><PERSON><PERSON>('active'),
              color: Colors.white,
              size: 20,
            )
                : Text(
              hour,
              key: ValueKey('hour'),
              style: GoogleFonts.dmSans(
                color: Colors.white70,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
      SizedBox(height: 4),
      Text(
        '${hour}h',
        style: GoogleFonts.dmSans(
          color: isCurrent ? color : Colors.white70,
          fontSize: 10,
        ),
      ),
    ],
  );
}