import 'package:flutter/material.dart';

import '../../../utils/text_utils.dart';
import 'build_stage_marker.dart';

Widget buildTimeline(
    bool isActive,
    int currentStageIndex,
    List<IconData> stageIcons,
    ) {
  return Padding(
    padding: const EdgeInsets.only(top: 24, bottom: 6),
    child: LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.1,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Progress timeline with glow
              if (isActive)
                Positioned(
                  top: 40,
                  child: AnimatedContainer(
                    duration: Duration(milliseconds: 500),
                    width:
                    (constraints.maxWidth - 48) *
                        (currentStageIndex / (fastingStages.length - 1)),
                    height: 6,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue,
                          fastingStages[currentStageIndex]['color'] as Color,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(3),
                      boxShadow: [
                        BoxShadow(
                          color:
                          (fastingStages[currentStageIndex]['color']
                          as Color)
                              .withOpacity(0.5),
                          blurRadius: 6,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),

              // Stages markers - horizontal scrollable
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 60,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    physics: BouncingScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(fastingStages.length, (i) {
                        final stage = fastingStages[i];
                        final isPastOrCurrent =
                            isActive && i <= currentStageIndex;

                        return Container(
                          width: 60, // Fixed width for each marker
                          child: buildStageMarker(
                            stage['hours'].toString(),
                            isPastOrCurrent
                                ? (stage['color'] as Color)
                                : Colors.grey[700]!,
                            isPastOrCurrent,
                            i == currentStageIndex,
                            stageIcons[i],
                          ),
                        );
                      }),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}
