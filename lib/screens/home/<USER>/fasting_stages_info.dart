import 'package:fasttime/models/fasting_day.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../utils/text_utils.dart';
import '../../../utils/time_utils.dart';
import 'build_main_card.dart';

Widget buildFastingStagesInfo(FastingDay? fastingDay) {
  final isActive = fastingDay?.isActive ?? false;
  int currentStageIndex = 0;

  if (isActive && fastingDay != null) {
    final now = DateTime.now();
    final startDateTime = getStartDateTime(fastingDay);
    final elapsed = now.difference(startDateTime).inHours;

    // Find current stage index
    for (int i = fastingStages.length - 1; i >= 0; i--) {
      if (elapsed >= fastingStages[i]['hours']) {
        currentStageIndex = i;
        break;
      }
    }
  }

  // List of stage icons for visualization
  final List<IconData> stageIcons = [
    Icons.lunch_dining, // Fed State
    Icons.hourglass_empty, // Post-Absorptive
    Icons.trending_down, // Early Fasting
    Icons.local_fire_department, // Fat Burning
    Icons.flash_on, // Ketosis
    Icons.recycling, // Autophagy
    Icons.fitness_center, // Growth Hormone
    Icons.auto_fix_high, // Deep Autophagy
  ];

  return AnimatedOpacity(
    opacity: 1.0,
    duration: Duration(milliseconds: 800),
    child: Container(
      constraints: BoxConstraints(
        minHeight: 100, // Ensure minimum height
      ),
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with animation
            stageBuildHeader(isActive, currentStageIndex, stageIcons),

            // Main card with frosted glass effect
            buildMainCard(
              isActive,
              currentStageIndex,
              stageIcons,
              fastingDay,
            ),
          ],
        ),
      ),
    ),
  );
}

Widget stageBuildHeader(
    bool isActive,
    int currentStageIndex,
    List<IconData> stageIcons,
    ) {
  return TweenAnimationBuilder<double>(
    tween: Tween<double>(begin: 0.0, end: 1.0),
    duration: Duration(milliseconds: 800),
    curve: Curves.easeOutQuart,
    builder: (context, value, child) {
      return Transform.translate(
        offset: Offset(0, 20 * (1 - value)),
        child: Opacity(opacity: value, child: child),
      );
    },
    child: Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(Icons.timer, color: Colors.white, size: 22),
              SizedBox(width: 8),
              Text(
                'Fasting Stages',
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          if (isActive)
            AnimatedContainer(
              duration: Duration(milliseconds: 300),
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: fastingStages[currentStageIndex]['color'] as Color,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color:
                    (fastingStages[currentStageIndex]['color'] as Color)
                        .withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    stageIcons[currentStageIndex],
                    color: Colors.white,
                    size: 14,
                  ),
                  SizedBox(width: 4),
                  Text(
                    fastingStages[currentStageIndex]['name'] as String,
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    ),
  );
}


