import 'package:fasttime/services/fasting_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:fasttime/route/router_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fasttime/widgets/modern_date_time_picker.dart';
import 'package:fasttime/blocs/auth/auth_bloc.dart';
import 'package:fasttime/blocs/auth/auth_state.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Settings state
  bool _notificationsEnabled = true;
  bool _darkThemeEnabled = true;
  String _fastingProtocol = '16:8';
  TimeOfDay _fastingStartTime = const TimeOfDay(hour: 20, minute: 0);
  bool _useMetricSystem = true;
  bool _showMotivationalQuotes = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _animationController.forward();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _darkThemeEnabled = prefs.getBool('dark_theme_enabled') ?? true;
        _fastingProtocol = prefs.getString('fasting_protocol') ?? '16:8';
        _useMetricSystem = prefs.getBool('use_metric_system') ?? true;
        _showMotivationalQuotes =
            prefs.getBool('show_motivational_quotes') ?? true;

        final startHour = prefs.getInt('fasting_start_hour') ?? 20;
        final startMinute = prefs.getInt('fasting_start_minute') ?? 0;
        _fastingStartTime = TimeOfDay(hour: startHour, minute: startMinute);

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: Text(
          'Logout',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await FirebaseAuth.instance.signOut();
                if (mounted) {
                  GoRouter.of(context).goNamed(RouteConstants.auth);
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to logout: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Logout',
              style: GoogleFonts.dmSans(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  // Add this method to your settings scree

  // Add this to your settings list

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFF0A0E21),
        body: Center(child: CircularProgressIndicator(color: Colors.white)),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildAppBar(),
            SliverFadeTransition(
              opacity: _fadeAnimation,
              sliver: SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildUserSection(),
                      const SizedBox(height: 24),
                      _buildSettingsSection(),
                      const SizedBox(height: 24),
                      _buildAccountSection(),
                      const SizedBox(height: 24),
                      _buildAboutSection(),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: _saveSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF6B4EFF),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Text(
            'Save Settings',
            style: GoogleFonts.dmSans(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('dark_theme_enabled', _darkThemeEnabled);
      await prefs.setString('fasting_protocol', _fastingProtocol);
      await prefs.setBool('use_metric_system', _useMetricSystem);
      await prefs.setBool('show_motivational_quotes', _showMotivationalQuotes);
      await prefs.setInt('fasting_start_hour', _fastingStartTime.hour);
      await prefs.setInt('fasting_start_minute', _fastingStartTime.minute);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Settings saved',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF6B4EFF),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save settings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: const Color(0xFF0A0E21),
      leading: IconButton(

        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => GoRouter.of(context).goNamed(RouteConstants.home),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Settings',
          style: GoogleFonts.dmMono(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF6B4EFF).withOpacity(0.4),
                const Color(0xFF0A0E21).withOpacity(0.0),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserSection() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        final user = FirebaseAuth.instance.currentUser;
        String displayName = 'User';
        String email = 'No email';

        if (authState is Authenticated) {
          displayName = authState.displayName ?? user?.displayName ?? 'User';
          email = authState.email ?? user?.email ?? 'No email';
        } else if (user != null) {
          displayName = user.displayName ?? 'User';
          email = user.email ?? 'No email';
        }

        return _buildUserCard(displayName, email);
      },
    );
  }

  Widget _buildUserCard(String displayName, String email) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFF6B4EFF).withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
                style: GoogleFonts.dmSans(
                  color: const Color(0xFF6B4EFF),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayName,
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  email,
                  style: GoogleFonts.dmSans(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout, color: Colors.redAccent),
            tooltip: 'Logout',
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'App Settings',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildSettingCard(
          children: [
            _buildSwitchSetting(
              title: 'Notifications',
              subtitle: 'Receive reminders and updates',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
            const Divider(color: Colors.white10),
            _buildSwitchSetting(
              title: 'Dark Theme',
              subtitle: 'Use dark theme throughout the app',
              value: _darkThemeEnabled,
              onChanged: (value) {
                setState(() {
                  _darkThemeEnabled = value;
                });
              },
            ),
            const Divider(color: Colors.white10),
            _buildDropdownSetting(
              title: 'Fasting Protocol',
              subtitle: 'Default fasting schedule',
              value: _fastingProtocol,
              options: const ['16:8', '18:6', '20:4', '14:10', 'OMAD'],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _fastingProtocol = value;
                  });
                }
              },
            ),
            const Divider(color: Colors.white10),
            _buildTimeSetting(
              title: 'Default Fasting Start Time',
              subtitle: 'When your fast typically begins',
              value: _fastingStartTime,
              onTap: () async {
                // Create a DateTime from the current time for the modern picker
                final now = DateTime.now();
                final currentDateTime = DateTime(
                  now.year,
                  now.month,
                  now.day,
                  _fastingStartTime.hour,
                  _fastingStartTime.minute,
                );

                final selectedDateTime = await ModernDateTimePicker.show(
                  context: context,
                  initialDateTime: currentDateTime,
                  label: 'Select Fasting Start Time',
                  accentColor: const Color(0xFF6B4EFF),
                  backgroundColor: const Color(0xFF0A0E21),
                );

                if (selectedDateTime != null) {
                  setState(() {
                    _fastingStartTime = TimeOfDay.fromDateTime(
                      selectedDateTime,
                    );
                  });
                }
              },
            ),
            const Divider(color: Colors.white10),
            _buildSwitchSetting(
              title: 'Metric System',
              subtitle: 'Use kg/cm instead of lb/in',
              value: _useMetricSystem,
              onChanged: (value) {
                setState(() {
                  _useMetricSystem = value;
                });
              },
            ),
            const Divider(color: Colors.white10),
            _buildSwitchSetting(
              title: 'Motivational Quotes',
              subtitle: 'Show quotes on the home screen',
              value: _showMotivationalQuotes,
              onChanged: (value) {
                setState(() {
                  _showMotivationalQuotes = value;
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildSettingCard(
          children: [
            _buildActionSetting(
              title: 'Edit Profile',
              subtitle: 'Change your name and photo',
              icon: Icons.person_outline,
              onTap: () {
                GoRouter.of(context).pushNamed(RouteConstants.editProfile);
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Change Password',
              subtitle: 'Update your password',
              icon: Icons.lock_outline,
              onTap: () {
                GoRouter.of(context).pushNamed(RouteConstants.changePassword);
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Export Data',
              subtitle: 'Download your fasting history',
              icon: Icons.download_outlined,
              onTap: () {
                // Show export options
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Delete Account',
              subtitle: 'Permanently remove your account',
              icon: Icons.delete_outline,
              iconColor: Colors.redAccent,
              textColor: Colors.redAccent,
              onTap: () {
                // Show delete account confirmation
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildSettingCard(
          children: [
            _buildActionSetting(
              title: 'App Version',
              subtitle: '1.0.0',
              icon: Icons.info_outline,
              onTap: () {
                // Show app version details
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Terms of Service',
              subtitle: 'Read our terms and conditions',
              icon: Icons.description_outlined,
              onTap: () {
                context.pushNamed(RouteConstants.termsOfService);
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Privacy Policy',
              subtitle: 'How we handle your data',
              icon: Icons.privacy_tip_outlined,
              onTap: () {
                context.pushNamed(RouteConstants.privacyPolicy);
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'Contact Support',
              subtitle: 'Get help with the app',
              icon: Icons.support_agent_outlined,
              onTap: () {
                // Show contact options
              },
            ),
            const Divider(color: Colors.white10),
            _buildActionSetting(
              title: 'About Author',
              subtitle: 'Meet the developer and support the project',
              icon: Icons.person_outlined,
              onTap: () {
                context.pushNamed(RouteConstants.aboutAuthor);
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSettingCard({required List<Widget> children}) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.dmSans(
                    color: Colors.white60,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF6B4EFF),
            activeTrackColor: const Color(0xFF6B4EFF).withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSetting<T>({
    required String title,
    required String subtitle,
    required T value,
    required List<T> options,
    required ValueChanged<T?> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.dmSans(
                    color: Colors.white60,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButton<T>(
              value: value,
              items: options.map((option) {
                return DropdownMenuItem<T>(
                  value: option,
                  child: Text(
                    option.toString(),
                    style: GoogleFonts.dmSans(color: Colors.white),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
              icon: const Icon(Icons.arrow_drop_down, color: Colors.white70),
              underline: const SizedBox(),
              dropdownColor: const Color(0xFF2D2E43),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSetting({
    required String title,
    required String subtitle,
    required TimeOfDay value,
    required VoidCallback onTap,
  }) {
    final formattedTime =
        '${value.hourOfPeriod}:${value.minute.toString().padLeft(2, '0')} ${value.period == DayPeriod.am ? 'AM' : 'PM'}';

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.dmSans(
                      color: Colors.white60,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Text(
                    formattedTime,
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.access_time,
                    color: Colors.white70,
                    size: 18,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color iconColor = Colors.white70,
    Color textColor = Colors.white,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: iconColor),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.dmSans(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.dmSans(
                      color: Colors.white60,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: Colors.white30),
          ],
        ),
      ),
    );
  }
}
