import 'package:fasttime/blocs/body_metrics/body_metrics_bloc.dart';
import 'package:fasttime/blocs/body_metrics/body_metrics_event.dart';
import 'package:fasttime/blocs/body_metrics/body_metrics_state.dart';
import 'package:fasttime/models/body_metric.dart';
import 'package:fasttime/route/router_constants.dart';
import 'package:fasttime/services/body_metrics_service.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:fasttime/widgets/bottom_nav.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:math' as math;

import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../utils/color_utils.dart';
import '../utils/text_utils.dart';
import '../widgets/weight_trend_chart.dart';

class BodyMetricsPage extends StatefulWidget {
  const BodyMetricsPage({Key? key}) : super(key: key);

  @override
  State<BodyMetricsPage> createState() => _BodyMetricsPageState();
}

class _BodyMetricsPageState extends State<BodyMetricsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late AnimationController _rotateController;
  late AnimationController _pulseController;
  late BodyMetricsService _bodyMetricsService;

  // Cache for calculations
  final Map<String, dynamic> _calculationCache = {};
  DateTime? _lastMetricsUpdate;

  // Local state
  List<BodyMetric> _metrics = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _rotateController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _initializeBodyMetricsService();
  }

  Future<void> _initializeBodyMetricsService() async {
    try {
      _bodyMetricsService = BodyMetricsService();
      await _bodyMetricsService.init();
      await _loadBodyMetrics();
      _fadeController.forward();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize body metrics service: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadBodyMetrics() async {
    try {
      final metrics = _bodyMetricsService.getAllMetrics();
      setState(() {
        _metrics = metrics;
        _isLoading = false;
        _error = null;

        if (_metrics.isNotEmpty) {
          _calculationCache.clear();
          _lastMetricsUpdate = _metrics.last.date;
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load body metrics: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void didUpdateWidget(BodyMetricsPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Force clear cache and reload state
    if (_metrics.isNotEmpty) {
      _calculationCache.clear();
      _lastMetricsUpdate = _metrics.last.date;
      setState(() {}); // Force rebuild
    }
  }

  // Memoized calculations
  Map<String, dynamic> _calculateMetrics(List<BodyMetric> metrics) {
    if (metrics.isEmpty) return {};

    final cacheKey = metrics
        .map((m) => '${m.date.millisecondsSinceEpoch}-${m.weight}')
        .join('|');
    if (_calculationCache.containsKey(cacheKey)) {
      return _calculationCache[cacheKey]!;
    }

    final startWeight = metrics.first.weight;
    final currentWeight = metrics.last.weight;
    final totalChange = currentWeight - startWeight;
    final percentChange = (totalChange / startWeight * 100).toStringAsFixed(1);
    final weeklyChange = totalChange / (metrics.length / 7);
    final avgWeight =
        metrics.map((e) => e.weight).reduce((a, b) => a + b) / metrics.length;
    final minWeight = metrics.map((e) => e.weight).reduce(math.min);
    final maxWeight = metrics.map((e) => e.weight).reduce(math.max);
    final fluctuation = maxWeight - minWeight;

    final calculations = {
      'startWeight': startWeight,
      'currentWeight': currentWeight,
      'totalChange': totalChange,
      'percentChange': percentChange,
      'weeklyChange': weeklyChange,
      'avgWeight': avgWeight,
      'minWeight': minWeight,
      'maxWeight': maxWeight,
      'fluctuation': fluctuation,
    };

    _calculationCache[cacheKey] = calculations;
    return calculations;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BodyMetricsBloc, BodyMetricsState>(
      buildWhen: (previous, current) {
        // Always rebuild when metrics change
        return previous.metrics.length != current.metrics.length ||
            (previous.metrics.isNotEmpty &&
                current.metrics.isNotEmpty &&
                previous.metrics.last.date != current.metrics.last.date);
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: const Color(0xFF0A0E21),
          body: SafeArea(
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildAppBar(),
                SliverToBoxAdapter(
                  child: state.metrics.isEmpty
                      ? _buildEmptyState(context)
                      : _buildMetricsContent(context, state),
                ),
              ],
            ),
          ),
          bottomNavigationBar: const BottomNav(
            currentRoute: RouteConstants.bodyMetrics,
          ),
          floatingActionButton: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2BA62A).withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: FloatingActionButton(
              onPressed: () => _addNewMeasurement(context),
              backgroundColor: const Color(0xFF2BA62A),
              elevation: 0,
              child: const Icon(Icons.add, size: 28, color: Colors.white),
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        );
      },
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _rotateController.dispose();
    _pulseController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Body Metrics',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
        centerTitle: false,
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF0A0E21),
                const Color(0xFF0A0E21).withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white70),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white70),
            onPressed: () => _showInfoDialog(context),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: IconButton(
            icon: const Icon(Icons.bug_report, color: Colors.white70),
            onPressed: () => _showDiagnosticsDialog(context),
          ),
        ),
      ],
    );
  }

  void _addNewMeasurement(BuildContext context) {
    final currentMetrics = context.read<BodyMetricsBloc>().state.metrics;
    final lastMetric = currentMetrics.isNotEmpty ? currentMetrics.last : null;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _NewMeasurementSheet(
        onSave: (weight, height, date) {
          final bloc = context.read<BodyMetricsBloc>();
          bloc.add(
            AddBodyMetric(
              date: date,
              weight: weight,
              height: height ?? lastMetric?.height ?? 170.0,
            ),
          );

          Navigator.pop(context);

          bloc.stream.take(1).listen((state) {
            if (mounted && state.metrics.isNotEmpty) {
              _fadeController.forward(from: 0.0);
            }
          });
        },
        currentWeight: lastMetric?.weight,
        currentHeight: lastMetric?.height,
        weightData: currentMetrics
            .map(
              (e) => FlSpot(e.date.millisecondsSinceEpoch.toDouble(), e.weight),
            )
            .toList(),
      ),
    );
  }

  Widget _buildMetricsContent(BuildContext context, BodyMetricsState state) {
    return FadeTransition(
      opacity: _fadeController,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ), // Dynamic padding for bottom nav bar
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 16),
              _buildLatestMeasurements(state),
              const SizedBox(height: 24),
              _buildTabBar(),
              const SizedBox(height: 16),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.6,
                child: TabBarView(
                  controller: _tabController,
                  physics: const BouncingScrollPhysics(),
                  children: [
                    _buildProgressTab(state),
                    _buildAnalysisTab(state),
                    _buildHistoryTab(state),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
      ),
      child: TabBar(
        indicatorSize: TabBarIndicatorSize.tab,
        controller: _tabController,
        tabs: const [
          Tab(text: 'Progress'),
          Tab(text: 'Analysis'),
          Tab(text: 'History'),
        ],
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white60,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.blue.withOpacity(0.2),
          border: Border.all(color: Colors.blue.withOpacity(0.3), width: 1),
        ),
        dividerColor: Colors.transparent,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildLatestMeasurements(BodyMetricsState state) {
    if (state.metrics.isEmpty) return const SizedBox.shrink();

    final latestMetric = state.metrics.last;
    final previousMetric = state.metrics.length > 1
        ? state.metrics[state.metrics.length - 2]
        : null;

    // Calculate weight change
    String weightChange = "0.0";
    IconData changeIcon = Icons.horizontal_rule;
    Color changeColor = Colors.grey;

    if (previousMetric != null) {
      final diff = latestMetric.weight - previousMetric.weight;
      weightChange = diff.abs().toStringAsFixed(1);
      changeIcon = diff < 0 ? Icons.arrow_downward : Icons.arrow_upward;
      changeColor = diff < 0 ? Colors.green : Colors.red;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildWeightCard(latestMetric, weightChange, changeIcon, changeColor),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Height',
                  '${latestMetric.height.toStringAsFixed(1)} cm',
                  Icons.height,
                  Colors.purpleAccent,
                  context,
                  latestMetric,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'BMI',
                  latestMetric.bmi.toStringAsFixed(1),
                  Icons.health_and_safety,
                  getBMIColor(latestMetric.bmi),
                  context,
                  latestMetric,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RotationTransition(
              turns: Tween(begin: 0.0, end: 1.0).animate(_rotateController),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.monitor_weight_outlined,
                  size: 64,
                  color: Colors.blue.withOpacity(0.7),
                ),
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'No measurements yet',
              style: GoogleFonts.dmMono(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Start tracking your body metrics to see your progress over time',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _addNewMeasurement(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF448AFF),
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text('Add Measurement'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressTab(BodyMetricsState state) {
    if (state.metrics.isEmpty) return const SizedBox.shrink();
    if (state.metrics.length < 2) return _buildNotEnoughDataMessage();

    final calculations = _calculateMetrics(state.metrics);

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            const Text(
              'Weight Trend',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildWeightChart(state.metrics),
            const SizedBox(height: 16),
            _buildWeightSummary(calculations),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightSummary(Map<String, dynamic> calculations) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            'Starting',
            '${calculations['startWeight'].toStringAsFixed(1)} kg',
            Icons.play_arrow,
            Colors.blue,
          ),
          _buildSummaryItem(
            'Current',
            '${calculations['currentWeight'].toStringAsFixed(1)} kg',
            Icons.flag,
            Colors.green,
          ),
          _buildSummaryItem(
            'Change',
            '${calculations['totalChange'] > 0 ? '+' : ''}${calculations['totalChange'].toStringAsFixed(1)} kg\n${calculations['percentChange']}%',
            calculations['totalChange'] > 0
                ? Icons.trending_up
                : Icons.trending_down,
            calculations['totalChange'] > 0 ? Colors.red : Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisTab(BodyMetricsState state) {
    if (state.metrics.length < 2) {
      return _buildNotEnoughDataMessage();
    }

    final calculations = _calculateMetrics(state.metrics);

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          const Text(
            'Weight Analysis',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildAnalysisCards(calculations),
          const SizedBox(height: 24),
          _buildDetailedAnalysis(calculations),
          const SizedBox(height: 24),
          _buildBMIAnalysis(state.metrics.last),
        ],
      ),
    );
  }

  Widget _buildBMIAnalysis(BodyMetric metric) {
    final currentBMI = metric.bmi;
    final bmiCategory = getBMICategory(currentBMI);
    final bmiColor = getBMIColor(currentBMI);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BMI Analysis',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: bmiColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.health_and_safety, color: bmiColor, size: 24),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current BMI',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    currentBMI.toStringAsFixed(1),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: bmiColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  bmiCategory,
                  style: TextStyle(
                    color: bmiColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildBMIScale(currentBMI),
          const SizedBox(height: 16),
          Text(
            getBMIDescription(currentBMI),
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBMIScale(double bmi) {
    return Column(
      children: [
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: const LinearGradient(
              colors: [Colors.blue, Colors.green, Colors.orange, Colors.red],
              stops: [0.18, 0.45, 0.65, 0.8],
            ),
          ),
        ),
        const SizedBox(height: 8),
        Stack(
          clipBehavior: Clip.none,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Underweight',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 10,
                  ),
                ),
                Text(
                  'Normal',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 10,
                  ),
                ),
                Text(
                  'Overweight',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 10,
                  ),
                ),
                Text(
                  'Obese',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
            Positioned(
              left:
                  (bmi - 16) /
                  (40 - 16) *
                  MediaQuery.of(context).size.width *
                  0.7,
              top: -20,
              child: Column(
                children: [
                  Text(
                    bmi.toStringAsFixed(1),
                    style: TextStyle(
                      color: getBMIColor(bmi),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: getBMIColor(bmi),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: getBMIColor(bmi).withOpacity(0.5),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailedAnalysis(Map<String, dynamic> calculations) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Detailed Stats',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow(
            'Average Weight',
            '${calculations['avgWeight'].toStringAsFixed(1)} kg',
            Icons.calculate,
            Colors.blue,
          ),
          const Divider(height: 24, color: Colors.white10),
          _buildDetailRow(
            'Min Weight',
            '${calculations['minWeight'].toStringAsFixed(1)} kg',
            Icons.arrow_downward,
            Colors.green,
          ),
          const Divider(height: 24, color: Colors.white10),
          _buildDetailRow(
            'Max Weight',
            '${calculations['maxWeight'].toStringAsFixed(1)} kg',
            Icons.arrow_upward,
            Colors.red,
          ),
          const Divider(height: 24, color: Colors.white10),
          _buildDetailRow(
            'Fluctuation',
            '${calculations['fluctuation'].toStringAsFixed(1)} kg',
            Icons.swap_vert,
            Colors.amber,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Text(
          label,
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryTab(BodyMetricsState state) {
    if (state.metrics.isEmpty) return const SizedBox.shrink();

    final sortedMetrics = List<BodyMetric>.from(state.metrics)
      ..sort((a, b) => b.date.compareTo(a.date));

    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: sortedMetrics.length,
      itemBuilder: (context, index) {
        final metric = sortedMetrics[index];
        final previousMetric = index < sortedMetrics.length - 1
            ? sortedMetrics[index + 1]
            : null;

        double? change;
        if (previousMetric != null) {
          change = metric.weight - previousMetric.weight;
        }

        return Dismissible(
          key: Key(metric.date.toString()),
          background: Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.8),
              borderRadius: BorderRadius.circular(16),
            ),
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(right: 20),
            child: const Icon(Icons.delete, color: Colors.white),
          ),
          direction: DismissDirection.endToStart,
          confirmDismiss: (direction) async {
            return await showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  backgroundColor: const Color(0xFF1D1E33),
                  title: const Text(
                    'Confirm Deletion',
                    style: TextStyle(color: Colors.white),
                  ),
                  content: const Text(
                    'Are you sure you want to delete this measurement?',
                    style: TextStyle(color: Colors.white70),
                  ),
                  actions: <Widget>[
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text(
                        'Delete',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                );
              },
            );
          },
          onDismissed: (direction) {
            context.read<BodyMetricsBloc>().add(
              DeleteBodyMetric(metric, date: metric.date),
            );
          },
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF1D1E33),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              leading: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.monitor_weight_outlined,
                  color: Colors.blue,
                ),
              ),
              title: Row(
                children: [
                  Text(
                    DateFormat('MMM dd, yyyy').format(metric.date),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (change != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: (change > 0 ? Colors.red : Colors.green)
                            .withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${change > 0 ? '+' : ''}${change.toStringAsFixed(1)} kg',
                        style: TextStyle(
                          fontSize: 12,
                          color: change > 0 ? Colors.red : Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    _buildHistoryDetail(
                      'Weight',
                      '${metric.weight.toStringAsFixed(1)} kg',
                    ),
                    const SizedBox(width: 16),
                    _buildHistoryDetail(
                      'Height',
                      '${metric.height.toStringAsFixed(1)} cm',
                    ),
                    const SizedBox(width: 16),
                    _buildHistoryDetail('BMI', metric.bmi.toStringAsFixed(1)),
                  ],
                ),
              ),
              trailing: IconButton(
                icon: const Icon(Icons.edit, color: Colors.white70),
                onPressed: () => _editMeasurement(context, metric),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHistoryDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.6)),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _editMeasurement(BuildContext context, BodyMetric metric) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _NewMeasurementSheet(
        onSave: (weight, height, date) {
          context.read<BodyMetricsBloc>().add(
            UpdateBodyMetric(
              originalDate: metric.date,
              date: date,
              weight: weight,
              height: height ?? metric.height,
            ),
          );
          _fadeController.forward(from: 0.0);
          Navigator.pop(context);
        },
        currentWeight: metric.weight,
        currentHeight: metric.height,
        initialDate: metric.date,
        isEditing: true,
      ),
    );
  }

  Widget _buildNotEnoughDataMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ScaleTransition(
              scale: Tween<double>(
                begin: 0.8,
                end: 1.0,
              ).animate(_pulseController),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.info_outline,
                  size: 48,
                  color: Colors.amber.withOpacity(0.7),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Not enough data',
              style: GoogleFonts.dmMono(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Add at least one more measurement to see your progress over time',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _addNewMeasurement(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF448AFF),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text('Add Measurement'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightCard(
    BodyMetric metric,
    String weightChange,
    IconData changeIcon,
    Color changeColor,
  ) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.monitor_weight_outlined,
                      color: Colors.blue,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Weight',
                        style: GoogleFonts.dmSans(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${metric.weight.toStringAsFixed(1)} kg',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  if (weightChange != "0.0")
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: changeColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          Icon(changeIcon, color: changeColor, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            '$weightChange kg',
                            style: GoogleFonts.dmSans(
                              color: changeColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Last updated on ${DateFormat('MMMM d, yyyy').format(metric.date)}',
                style: GoogleFonts.dmSans(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
    BodyMetric metric,
  ) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(icon, color: color, size: 16),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: GoogleFonts.dmSans(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeightChart(List<BodyMetric> metrics) {
    return WeightTrendChart(
      data: metrics.map((metric) {
        return FlSpot(
          metric.date.millisecondsSinceEpoch.toDouble(),
          metric.weight,
        );
      }).toList(),
      minY: 0,
      maxY: 150,
    );
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text(
          'About Body Metrics',
          style: TextStyle(color: Colors.white),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoSection(
                'Weight Tracking',
                'Track your weight over time to monitor your progress. Regular measurements help you stay accountable.',
                Icons.monitor_weight_outlined,
                Colors.blue,
              ),
              const SizedBox(height: 16),
              _buildInfoSection(
                'BMI Calculation',
                'Body Mass Index (BMI) is calculated using your weight and height. It provides a general indication of whether you have a healthy body weight.',
                Icons.health_and_safety,
                Colors.green,
              ),
              const SizedBox(height: 16),
              _buildInfoSection(
                'Data Analysis',
                'View trends and statistics to understand your progress better. The app analyzes your data to provide insights.',
                Icons.analytics,
                Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildInfoSection(
                'State Restoration',
                'Your data is automatically saved and restored when you reopen the app, ensuring you never lose your progress.',
                Icons.restore,
                Colors.amber,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            description,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisCards(Map<String, dynamic> calculations) {
    return Row(
      children: [
        Expanded(
          child: _buildAnalysisCard(
            'Total Change',
            '${calculations['totalChange'] > 0 ? '+' : ''}${calculations['totalChange'].toStringAsFixed(1)} kg',
            calculations['totalChange'] > 0
                ? Icons.trending_up
                : Icons.trending_down,
            calculations['totalChange'] > 0 ? Colors.red : Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildAnalysisCard(
            'Weekly Avg',
            '${calculations['weeklyChange'] > 0 ? '+' : ''}${calculations['weeklyChange'].toStringAsFixed(2)} kg',
            calculations['weeklyChange'] > 0
                ? Icons.trending_up
                : Icons.trending_down,
            calculations['weeklyChange'] > 0 ? Colors.red : Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showDiagnosticsDialog(BuildContext context) async {
    final bloc = context.read<BodyMetricsBloc>();

    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    // Get diagnostic info
    final diagnostics = await bloc.diagnoseIssues();

    // Hide loading
    Navigator.of(context).pop();

    // Show diagnostics
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Metrics Diagnostics'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              for (final entry in diagnostics.entries)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text('${entry.key}: ${entry.value}'),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Show loading
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) =>
                    const Center(child: CircularProgressIndicator()),
              );

              // Force reload
              await bloc.forceReload();

              // Hide loading
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Metrics reloaded'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('Force Reload'),
          ),
        ],
      ),
    );
  }
}

class _NewMeasurementSheet extends StatefulWidget {
  final Function(double weight, double? height, DateTime date) onSave;
  final double? currentWeight;
  final double? currentHeight;
  final List<FlSpot>? weightData;
  final DateTime? initialDate;
  final bool isEditing;

  const _NewMeasurementSheet({
    required this.onSave,
    this.currentWeight,
    this.currentHeight,
    this.weightData,
    this.initialDate,
    this.isEditing = false,
  });

  @override
  State<_NewMeasurementSheet> createState() => _NewMeasurementSheetState();
}

class _NewMeasurementSheetState extends State<_NewMeasurementSheet>
    with SingleTickerProviderStateMixin {
  late TextEditingController _weightController;
  late TextEditingController _heightController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  late DateTime selectedDate;
  bool _updateHeight = false;
  double? _bmi;

  @override
  void initState() {
    super.initState();
    _weightController = TextEditingController(
      text: widget.currentWeight?.toString() ?? '',
    );
    _heightController = TextEditingController(
      text: widget.currentHeight?.toString() ?? '',
    );
    selectedDate = widget.initialDate ?? DateTime.now();
    _calculateBMI();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ).drive(Tween<double>(begin: 0.0, end: 1.0));

    _animationController.forward();
  }

  @override
  void dispose() {
    _weightController.dispose();
    _heightController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _calculateBMI() {
    final weight = double.tryParse(_weightController.text);
    final height = double.tryParse(_heightController.text);

    if (weight != null && height != null && height > 0) {
      // BMI = weight (kg) / (height (m))²
      final heightInMeters = height / 100;
      setState(() {
        _bmi = weight / (heightInMeters * heightInMeters);
      });
    } else {
      setState(() {
        _bmi = null;
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF448AFF),
              onPrimary: Colors.white,
              surface: Color(0xFF1D1E33),
              onSurface: Colors.white,
            ),
            dialogBackgroundColor: const Color(0xFF0A0E21),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _animation.value) * 100),
          child: Opacity(
            opacity: _animation.value.clamp(0.0, 1.0),
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xFF0A0E21),
                borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
              ),
              padding: EdgeInsets.fromLTRB(
                24,
                20,
                24,
                MediaQuery.of(context).viewInsets.bottom + 24,
              ),
              child: child,
            ),
          ),
        );
      },
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              widget.isEditing ? 'Edit Measurement' : 'New Measurement',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildDateSelector(),
            const SizedBox(height: 24),
            _buildWeightField(),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _updateHeight,
                  onChanged: (value) {
                    setState(() {
                      _updateHeight = value ?? false;
                    });
                    _calculateBMI();
                  },
                  activeColor: const Color(0xFF448AFF),
                ),
                const Text(
                  'Update height',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState: _updateHeight
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              firstChild: const SizedBox(height: 0),
              secondChild: Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildHeightField(),
              ),
            ),
            if (_bmi != null) _buildBMIIndicator(),
            const SizedBox(height: 24),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFF1D1E33),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: Colors.white70),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat('MMMM d, yyyy').format(selectedDate),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Spacer(),
            const Icon(Icons.arrow_drop_down, color: Colors.white70),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Weight (kg)',
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _weightController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          style: const TextStyle(color: Colors.white),
          onChanged: (_) => _calculateBMI(),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1D1E33),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFF448AFF)),
            ),
            suffixText: 'kg',
            suffixStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
          ),
        ),
      ],
    );
  }

  Widget _buildHeightField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Height (cm)',
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _heightController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          style: const TextStyle(color: Colors.white),
          onChanged: (_) => _calculateBMI(),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1D1E33),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFF448AFF)),
            ),
            suffixText: 'cm',
            suffixStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
          ),
        ),
      ],
    );
  }

  Widget _buildBMIIndicator() {
    final bmi = _bmi!;
    final Color bmiColor = getBMIColor(bmi);
    final String category = getBMICategory(bmi);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1D1E33),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: bmiColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.health_and_safety, color: bmiColor),
              const SizedBox(width: 8),
              Text(
                'Calculated BMI',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: bmiColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  category,
                  style: TextStyle(
                    color: bmiColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            bmi.toStringAsFixed(1),
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () {
          final weight = double.tryParse(_weightController.text);
          final height = _updateHeight
              ? double.tryParse(_heightController.text)
              : widget.currentHeight;

          if (weight != null && (!_updateHeight || height != null)) {
            widget.onSave(weight, _updateHeight ? height : null, selectedDate);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Please enter valid measurements',
                  style: GoogleFonts.poppins(color: Colors.white),
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF448AFF),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Text(
          widget.isEditing ? 'Update' : 'Save',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
