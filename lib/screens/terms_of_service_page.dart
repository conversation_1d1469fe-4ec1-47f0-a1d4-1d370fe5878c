import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0A0E21),
        title: Text(
          'Terms of Service',
          style: GoogleFonts.dmMono(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => GoRouter.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Terms of Service',
                style: GoogleFonts.dmMono(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Last updated: ${DateTime.now().year}',
                style: GoogleFonts.dmSans(fontSize: 14, color: Colors.white70),
              ),
              const SizedBox(height: 24),

              _buildSection(
                '1. Acceptance of Terms',
                'By downloading, installing, or using the FastTime app, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our service.',
              ),

              _buildSection(
                '2. Description of Service',
                'FastTime is a mobile application designed to help users track their intermittent fasting schedules, monitor progress, and maintain healthy fasting habits.',
              ),

              _buildSection(
                '3. User Accounts',
                'You may be required to create an account to use certain features of the app. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.',
              ),

              _buildSection(
                '4. Privacy and Data',
                'Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.',
              ),

              _buildSection(
                '5. Health Disclaimer',
                'FastTime is not intended to provide medical advice. Before starting any fasting regimen, please consult with a healthcare professional. The app is for informational purposes only.',
              ),

              _buildSection(
                '6. Prohibited Uses',
                'You agree not to use the app for any unlawful purpose or in any way that could damage, disable, or impair the service.',
              ),

              _buildSection(
                '7. Intellectual Property',
                'The app and its original content, features, and functionality are owned by FastTime and are protected by international copyright, trademark, and other intellectual property laws.',
              ),

              _buildSection(
                '8. Limitation of Liability',
                'In no event shall FastTime be liable for any indirect, incidental, special, consequential, or punitive damages arising out of your use of the app.',
              ),

              _buildSection(
                '9. Changes to Terms',
                'We reserve the right to modify these terms at any time. We will notify users of any material changes through the app or other means.',
              ),

              _buildSection(
                '10. Contact Information',
                'If you have any questions about these Terms of Service, please contact us through the app\'s support feature.',
              ),

              const SizedBox(height: 32),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF1D1E33),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'By continuing to use FastTime, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',
                  style: GoogleFonts.dmSans(
                    fontSize: 14,
                    color: Colors.white70,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: GoogleFonts.dmSans(
              fontSize: 14,
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
