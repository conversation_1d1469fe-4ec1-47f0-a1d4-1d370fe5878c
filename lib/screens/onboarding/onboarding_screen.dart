import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

import '../../route/router_constants.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _backgroundController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int _currentSlide = 0;
  Timer? _slideTimer;
  bool _isAutoPlaying = true;

  final List<OnboardingSlide> _slides = [
    OnboardingSlide(
      title: 'Smart Fasting Timer',
      subtitle: 'Track Your Journey',
      description:
          'Monitor your fasting windows with precision timing and real-time progress updates. Get notified when to start and end your fasts.',
      icon: Icons.timer_outlined,
      color: const Color(0xFF6B4EFF),
      features: [
        'Real-time fasting timer',
        'Custom fasting protocols',
        'Smart notifications',
        'Progress tracking',
      ],
    ),
    OnboardingSlide(
      title: 'Health Analytics',
      subtitle: 'Monitor Your Progress',
      description:
          'Track your weight, body metrics, and fasting streaks with detailed charts and insights to optimize your health journey.',
      icon: Icons.trending_up,
      color: const Color(0xFF4F8EFF),
      features: [
        'Weight tracking',
        'Body composition metrics',
        'Detailed analytics',
        'Progress charts',
      ],
    ),
    OnboardingSlide(
      title: 'Achievement System',
      subtitle: 'Stay Motivated',
      description:
          'Unlock badges, reach milestones, and celebrate your success. Join a community of thousands achieving their health goals.',
      icon: Icons.emoji_events_outlined,
      color: const Color(0xFF9B59B6),
      features: [
        'Achievement badges',
        'Milestone tracking',
        'Community support',
        'Success stories',
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();

    _pageController = PageController();

    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );

    _fadeController.forward();
    _startAutoSlide();
  }

  void _startAutoSlide() {
    _slideTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_isAutoPlaying && mounted) {
        if (_currentSlide < _slides.length - 1) {
          _nextSlide();
        } else {
          _slideTimer?.cancel();
          _isAutoPlaying = false;
        }
      }
    });
  }

  void _nextSlide() {
    if (_currentSlide < _slides.length - 1) {
      _currentSlide++;
      _pageController.animateToPage(
        _currentSlide,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousSlide() {
    if (_currentSlide > 0) {
      _currentSlide--;
      _pageController.animateToPage(
        _currentSlide,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToSignIn() {
    context.goNamed(RouteConstants.signIn);
  }

  @override
  void dispose() {
    _slideTimer?.cancel();
    _pageController.dispose();
    _backgroundController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF1A1A2E), Color(0xFF16213E), Color(0xFF0F3460)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),

            // Main content
            SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Header
                    _buildHeader(),

                    // Slideshow
                    Expanded(child: _buildSlideshow()),

                    // Navigation controls
                    _buildNavigationControls(),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            ...List.generate(6, (index) {
              final offset =
                  (_backgroundController.value * 2 * pi + index * pi / 3);
              final x =
                  cos(offset) * 80 + MediaQuery.of(context).size.width / 2;
              final y =
                  sin(offset) * 120 + MediaQuery.of(context).size.height / 2;

              return Positioned(
                left: x,
                top: y,
                child: Opacity(
                  opacity:
                      0.05 +
                      0.05 * sin(_backgroundController.value * 2 * pi + index),
                  child: Container(
                    width: 3 + sin(offset),
                    height: 3 + sin(offset),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _slides[_currentSlide].color.withValues(
                        alpha: 0.6,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        children: [
          // App logo
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  _slides[_currentSlide].color,
                  _slides[_currentSlide].color.withValues(alpha: 0.7),
                ],
              ),
            ),
            child: const Icon(
              Icons.timer_outlined,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'FastTime',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          // Skip button
          TextButton(
            onPressed: _goToSignIn,
            child: Text(
              'Skip',
              style: GoogleFonts.dmSans(color: Colors.white60, fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSlideshow() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentSlide = index;
          _isAutoPlaying = false;
        });
        _slideTimer?.cancel();
      },
      itemCount: _slides.length,
      itemBuilder: (context, index) {
        return _buildSlideContent(_slides[index]);
      },
    );
  }

  Widget _buildSlideContent(OnboardingSlide slide) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [slide.color, slide.color.withValues(alpha: 0.7)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: slide.color.withValues(alpha: 0.3),
                  blurRadius: 30,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(slide.icon, color: Colors.white, size: 60),
          ),

          const SizedBox(height: 40),

          // Title and subtitle
          Text(
            slide.title,
            style: GoogleFonts.dmSans(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            slide.subtitle,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              color: slide.color,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Description
          Text(
            slide.description,
            style: GoogleFonts.dmSans(
              fontSize: 16,
              color: Colors.white70,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Features
          ...slide.features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: slide.color, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        children: [
          // Page indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_slides.length, (index) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: _currentSlide == index ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _currentSlide == index
                      ? _slides[_currentSlide].color
                      : Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              );
            }),
          ),

          const SizedBox(height: 32),

          // Navigation buttons
          Row(
            children: [
              // Previous button
              if (_currentSlide > 0)
                Expanded(
                  child: OutlinedButton(
                    onPressed: _previousSlide,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Previous',
                      style: GoogleFonts.dmSans(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ),
                )
              else
                const Expanded(child: SizedBox()),

              const SizedBox(width: 16),

              // Next/Get Started button
              Expanded(
                child: ElevatedButton(
                  onPressed: _currentSlide == _slides.length - 1
                      ? _goToSignIn
                      : _nextSlide,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _slides[_currentSlide].color,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    _currentSlide == _slides.length - 1
                        ? 'Get Started'
                        : 'Next',
                    style: GoogleFonts.dmSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class OnboardingSlide {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;
  final List<String> features;

  OnboardingSlide({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
    required this.features,
  });
}
