import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ErrorService {
  /// Shows an error banner at the bottom of the screen
  static void showErrorBanner(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.redAccent,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
        duration: duration,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Formats error messages for better readability
  static String formatErrorMessage(dynamic error) {
    if (error == null) return 'An unknown error occurred';

    final message = error.toString();

    // Remove common error prefixes
    if (message.contains('Exception:')) {
      return message.split('Exception:').last.trim();
    }

    if (message.contains('Error:')) {
      return message.split('Error:').last.trim();
    }

    return message;
  }
}
