import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Service to handle authentication data persistence
class AuthPersistenceService {
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _userNameKey = 'user_name';
  static const String _authTimestampKey = 'auth_timestamp';
  static const String _authMethodKey = 'auth_method';
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _lastActiveKey = 'last_active';

  /// Persist user authentication data
  static Future<void> persistUserData(User user, {String authMethod = 'email_password'}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.setString(_userIdKey, user.uid),
        prefs.setString(_userEmailKey, user.email ?? ''),
        prefs.setString(_user<PERSON><PERSON><PERSON><PERSON>, user.displayName ?? ''),
        prefs.setInt(_authTimestamp<PERSON>ey, DateTime.now().millisecondsSinceEpoch),
        prefs.setString(_authMethodKey, authMethod),
        prefs.setInt(_lastActiveKey, DateTime.now().millisecondsSinceEpoch),
      ]);
      
      // Mark that user has completed first launch
      await prefs.setBool(_isFirstLaunchKey, false);
    } catch (e) {
      // Log error but don't throw - authentication can still work
      print('Error persisting user data: $e');
    }
  }

  /// Get stored user data
  static Future<Map<String, dynamic>?> getStoredUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final userId = prefs.getString(_userIdKey);
      if (userId == null) return null;
      
      return {
        'userId': userId,
        'email': prefs.getString(_userEmailKey),
        'displayName': prefs.getString(_userNameKey),
        'authTimestamp': prefs.getInt(_authTimestampKey),
        'authMethod': prefs.getString(_authMethodKey),
        'lastActive': prefs.getInt(_lastActiveKey),
      };
    } catch (e) {
      print('Error getting stored user data: $e');
      return null;
    }
  }

  /// Clear all authentication data
  static Future<void> clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.remove(_userIdKey),
        prefs.remove(_userEmailKey),
        prefs.remove(_userNameKey),
        prefs.remove(_authTimestampKey),
        prefs.remove(_authMethodKey),
        prefs.remove(_lastActiveKey),
      ]);
    } catch (e) {
      print('Error clearing auth data: $e');
    }
  }

  /// Update last active timestamp
  static Future<void> updateLastActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastActiveKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error updating last active: $e');
    }
  }

  /// Check if this is the first app launch
  static Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isFirstLaunchKey) ?? true;
    } catch (e) {
      return true;
    }
  }

  /// Mark first launch as completed
  static Future<void> markFirstLaunchCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isFirstLaunchKey, false);
    } catch (e) {
      print('Error marking first launch completed: $e');
    }
  }

  /// Check if user session is still valid (within 30 days)
  static Future<bool> isSessionValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final authTimestamp = prefs.getInt(_authTimestampKey);
      
      if (authTimestamp == null) return false;
      
      final authDate = DateTime.fromMillisecondsSinceEpoch(authTimestamp);
      final now = DateTime.now();
      final difference = now.difference(authDate);
      
      // Session is valid for 30 days
      return difference.inDays < 30;
    } catch (e) {
      return false;
    }
  }

  /// Verify stored user data matches current Firebase user
  static Future<bool> verifyUserDataIntegrity(User firebaseUser) async {
    try {
      final storedData = await getStoredUserData();
      if (storedData == null) return false;
      
      return storedData['userId'] == firebaseUser.uid;
    } catch (e) {
      return false;
    }
  }

  /// Sync stored data with Firebase user data
  static Future<void> syncWithFirebaseUser(User firebaseUser) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Update email if it changed
      if (firebaseUser.email != null) {
        final storedEmail = prefs.getString(_userEmailKey);
        if (storedEmail != firebaseUser.email) {
          await prefs.setString(_userEmailKey, firebaseUser.email!);
        }
      }
      
      // Update display name if it changed
      if (firebaseUser.displayName != null) {
        final storedName = prefs.getString(_userNameKey);
        if (storedName != firebaseUser.displayName) {
          await prefs.setString(_userNameKey, firebaseUser.displayName!);
        }
      }
      
      // Update last active
      await updateLastActive();
    } catch (e) {
      print('Error syncing with Firebase user: $e');
    }
  }

  /// Get authentication statistics for debugging
  static Future<Map<String, dynamic>> getAuthStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return {
        'hasStoredUser': prefs.getString(_userIdKey) != null,
        'authMethod': prefs.getString(_authMethodKey),
        'authTimestamp': prefs.getInt(_authTimestampKey),
        'lastActive': prefs.getInt(_lastActiveKey),
        'isFirstLaunch': prefs.getBool(_isFirstLaunchKey) ?? true,
        'sessionValid': await isSessionValid(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}
