import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';
import '../models/achievement.dart';
import '../models/fasting_day.dart';
import '../utils/text_utils.dart';

class AchievementService {
  static const String _boxName = 'achievements';
  late Box<String> _box; // Store plain JSON strings

  AchievementService();

  Future<void> init() async {
    // Adapters are registered in main.dart, just open the box
    _box = await Hive.openBox<String>(_boxName);
  }

  Future<void> unlockAchievement(Achievement achievement) async {
    if (!isAchievementUnlocked(achievement.id)) {
      final json = jsonEncode(achievement.toJson());
      // Store plain JSON string
      await _box.put(achievement.id, json);
    }
  }

  bool isAchievementUnlocked(String id) {
    return _box.containsKey(id);
  }

  List<Achievement> getAllAchievements() {
    return _box.values.map((jsonStr) {
      final json = jsonDecode(jsonStr);
      return Achievement.fromJson(json);
    }).toList()..sort((a, b) => b.unlockedDate.compareTo(a.unlockedDate));
  }

  int getTotalPoints() {
    return _box.values.fold(0, (sum, jsonStr) {
      final json = jsonDecode(jsonStr);
      final achievement = Achievement.fromJson(json);
      return sum + achievement.points;
    });
  }

  // Check for achievements based on fasting history
  Future<List<Achievement>> checkForNewAchievements(
    List<FastingDay> completedFasts,
  ) async {
    final List<Achievement> newAchievements = [];

    // First fast completed
    if (completedFasts.length == 1 && !isAchievementUnlocked('first_fast')) {
      final achievement = Achievement(
        id: 'first_fast',
        title: 'First Fast',
        description: 'Completed your first fasting session',
        iconName: 'trophy',
        unlockedDate: DateTime.now(),
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 3-day streak
    if (_hasConsecutiveDays(completedFasts, 3) &&
        !isAchievementUnlocked('streak_3')) {
      final achievement = Achievement(
        id: 'streak_3',
        title: '3-Day Streak',
        description: 'Completed fasts for 3 consecutive days',
        iconName: 'local_fire_department',
        unlockedDate: DateTime.now(),
        points: 30,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 7-day streak
    if (_hasConsecutiveDays(completedFasts, 7) &&
        !isAchievementUnlocked('streak_7')) {
      final achievement = Achievement(
        id: 'streak_7',
        title: 'Week Warrior',
        description: 'Completed fasts for 7 consecutive days',
        iconName: 'whatshot',
        unlockedDate: DateTime.now(),
        points: 70,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 14-day streak
    if (_hasConsecutiveDays(completedFasts, 14) &&
        !isAchievementUnlocked('streak_14')) {
      final achievement = Achievement(
        id: 'streak_14',
        title: 'Fortnight Champion',
        description: 'Completed fasts for 14 consecutive days',
        iconName: 'whatshot',
        unlockedDate: DateTime.now(),
        points: 140,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 30-day streak
    if (_hasConsecutiveDays(completedFasts, 30) &&
        !isAchievementUnlocked('streak_30')) {
      final achievement = Achievement(
        id: 'streak_30',
        title: 'Monthly Master',
        description: 'Completed fasts for 30 consecutive days',
        iconName: 'whatshot',
        unlockedDate: DateTime.now(),
        points: 300,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Long fast (24+ hours)
    if (_hasLongFast(completedFasts, 24 * 60) &&
        !isAchievementUnlocked('long_fast')) {
      final achievement = Achievement(
        id: 'long_fast',
        title: 'Endurance Master',
        description: 'Completed a fast of 24 hours or longer',
        iconName: 'timer',
        unlockedDate: DateTime.now(),
        points: 50,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Long fast (36+ hours)
    if (_hasLongFast(completedFasts, 36 * 60) &&
        !isAchievementUnlocked('long_fast_36')) {
      final achievement = Achievement(
        id: 'long_fast_36',
        title: 'Fasting Guru',
        description: 'Completed a fast of 36 hours or longer',
        iconName: 'timer',
        unlockedDate: DateTime.now(),
        points: 100,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Long fast (48+ hours)
    if (_hasLongFast(completedFasts, 48 * 60) &&
        !isAchievementUnlocked('long_fast_48')) {
      final achievement = Achievement(
        id: 'long_fast_48',
        title: 'Fasting Sage',
        description: 'Completed a fast of 48 hours or longer',
        iconName: 'timer',
        unlockedDate: DateTime.now(),
        points: 200,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Total fasts completed
    if (completedFasts.length >= 10 && !isAchievementUnlocked('total_10')) {
      final achievement = Achievement(
        id: 'total_10',
        title: 'Beginner Faster',
        description: 'Completed 10 fasting sessions',
        iconName: 'repeat',
        unlockedDate: DateTime.now(),
        points: 50,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    if (completedFasts.length >= 25 && !isAchievementUnlocked('total_25')) {
      final achievement = Achievement(
        id: 'total_25',
        title: 'Intermediate Faster',
        description: 'Completed 25 fasting sessions',
        iconName: 'repeat',
        unlockedDate: DateTime.now(),
        points: 100,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    if (completedFasts.length >= 50 && !isAchievementUnlocked('total_50')) {
      final achievement = Achievement(
        id: 'total_50',
        title: 'Advanced Faster',
        description: 'Completed 50 fasting sessions',
        iconName: 'repeat',
        unlockedDate: DateTime.now(),
        points: 200,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    if (completedFasts.length >= 100 && !isAchievementUnlocked('total_100')) {
      final achievement = Achievement(
        id: 'total_100',
        title: 'Fasting Expert',
        description: 'Completed 100 fasting sessions',
        iconName: 'repeat',
        unlockedDate: DateTime.now(),
        points: 500,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Early Bird achievement
    if (_hasStartedFastsAtTime(completedFasts, true) &&
        !isAchievementUnlocked('early_bird')) {
      final achievement = Achievement(
        id: 'early_bird',
        title: 'Early Bird',
        description: 'Started 5 fasts before 6 AM',
        iconName: 'wb_sunny',
        unlockedDate: DateTime.now(),
        points: 50,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    // Night Owl achievement
    if (_hasStartedFastsAtTime(completedFasts, false) &&
        !isAchievementUnlocked('night_owl')) {
      final achievement = Achievement(
        id: 'night_owl',
        title: 'Night Owl',
        description: 'Started 5 fasts after 8 PM',
        iconName: 'nights_stay',
        unlockedDate: DateTime.now(),
        points: 50,
      );
      await unlockAchievement(achievement);
      newAchievements.add(achievement);
    }

    return newAchievements;
  }

  bool _hasConsecutiveDays(List<FastingDay> fasts, int days) {
    if (fasts.length < days) return false;

    // Sort by date descending
    final sortedFasts = List<FastingDay>.from(fasts)
      ..sort((a, b) => b.date.compareTo(a.date));

    for (int i = 0; i < days - 1; i++) {
      final currentDate = sortedFasts[i].date;
      final nextDate = sortedFasts[i + 1].date;

      // Check if dates are consecutive
      if (currentDate.difference(nextDate).inDays != 1) {
        return false;
      }
    }

    return true;
  }

  bool _hasLongFast(List<FastingDay> fasts, int minutes) {
    return fasts.any(
      (fast) =>
          fast.actualFastingMinutes != null &&
          fast.actualFastingMinutes! >= minutes,
    );
  }

  // Helper method to check if user has started fasts at specific times
  bool _hasStartedFastsAtTime(
    List<FastingDay> completedFasts,
    bool isEarlyMorning,
  ) {
    int count = 0;

    for (final fast in completedFasts) {
      final startHour = int.parse(fast.startTime.split(':')[0]);

      if (isEarlyMorning && startHour < 6) {
        count++;
      } else if (!isEarlyMorning && startHour >= 20) {
        count++;
      }

      if (count >= 5) {
        return true;
      }
    }

    return false;
  }

  // Predefined list of all possible achievements

  List<Achievement> getAllPossibleAchievements() {
    final now = DateTime.now();
    return [
      // Existing achievements
      Achievement(
        id: 'first_fast',
        title: 'First Fast',
        description: 'Completed your first fasting session',
        iconName: 'trophy',
        unlockedDate: now,
        points: 10,
      ),
      Achievement(
        id: 'streak_3',
        title: '3-Day Streak',
        description: 'Completed fasts for 3 consecutive days',
        iconName: 'local_fire_department',
        unlockedDate: now,
        points: 30,
      ),
      Achievement(
        id: 'streak_7',
        title: 'Week Warrior',
        description: 'Completed fasts for 7 consecutive days',
        iconName: 'whatshot',
        unlockedDate: now,
        points: 70,
      ),
      Achievement(
        id: 'long_fast',
        title: 'Endurance Master',
        description: 'Completed a fast of 24 hours or longer',
        iconName: 'timer',
        unlockedDate: now,
        points: 50,
      ),

      // New achievements
      Achievement(
        id: 'streak_14',
        title: 'Fortnight Champion',
        description: 'Completed fasts for 14 consecutive days',
        iconName: 'whatshot',
        unlockedDate: now,
        points: 140,
      ),
      Achievement(
        id: 'streak_30',
        title: 'Monthly Master',
        description: 'Completed fasts for 30 consecutive days',
        iconName: 'whatshot',
        unlockedDate: now,
        points: 300,
      ),
      Achievement(
        id: 'long_fast_36',
        title: 'Fasting Guru',
        description: 'Completed a fast of 36 hours or longer',
        iconName: 'timer',
        unlockedDate: now,
        points: 100,
      ),
      Achievement(
        id: 'long_fast_48',
        title: 'Fasting Sage',
        description: 'Completed a fast of 48 hours or longer',
        iconName: 'timer',
        unlockedDate: now,
        points: 200,
      ),
      Achievement(
        id: 'total_10',
        title: 'Beginner Faster',
        description: 'Completed 10 fasting sessions',
        iconName: 'repeat',
        unlockedDate: now,
        points: 50,
      ),
      Achievement(
        id: 'total_25',
        title: 'Intermediate Faster',
        description: 'Completed 25 fasting sessions',
        iconName: 'repeat',
        unlockedDate: now,
        points: 100,
      ),
      Achievement(
        id: 'total_50',
        title: 'Advanced Faster',
        description: 'Completed 50 fasting sessions',
        iconName: 'repeat',
        unlockedDate: now,
        points: 200,
      ),
      Achievement(
        id: 'total_100',
        title: 'Fasting Expert',
        description: 'Completed 100 fasting sessions',
        iconName: 'repeat',
        unlockedDate: now,
        points: 500,
      ),
      Achievement(
        id: 'perfect_week',
        title: 'Perfect Week',
        description: 'Completed all planned fasts in a week',
        iconName: 'check_circle',
        unlockedDate: now,
        points: 100,
      ),
      Achievement(
        id: 'early_bird',
        title: 'Early Bird',
        description: 'Started 5 fasts before 6 AM',
        iconName: 'wb_sunny',
        unlockedDate: now,
        points: 50,
      ),
      Achievement(
        id: 'night_owl',
        title: 'Night Owl',
        description: 'Started 5 fasts after 8 PM',
        iconName: 'nights_stay',
        unlockedDate: now,
        points: 50,
      ),
    ];
  }

  // Get all achievements with locked status
  List<Map<String, dynamic>> getAllAchievementsWithStatus() {
    final unlockedAchievements = getAllAchievements();
    final allPossibleAchievements = getAllPossibleAchievements();

    return allPossibleAchievements.map((achievement) {
      final isUnlocked = isAchievementUnlocked(achievement.id);

      // If unlocked, get the actual unlocked date
      final unlockedAchievement = unlockedAchievements.firstWhere(
        (a) => a.id == achievement.id,
        orElse: () => achievement,
      );

      return {
        'achievement': isUnlocked ? unlockedAchievement : achievement,
        'isUnlocked': isUnlocked,
      };
    }).toList();
  }

  Future<void> close() async {
    if (_box.isOpen) {
      await _box.close();
    }
  }
}
