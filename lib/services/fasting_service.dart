import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';
import '../models/fasting_day.dart';
import 'dart:async';

class FastingService {
  static const String _boxName = 'fasting_days';
  late Box<String> _box; // Store plain JSON strings
  StreamController<bool>? _controller;
  bool _isDisposed = false;

  FastingService();

  Future<void> init() async {
    if (_isDisposed) return;
    // Adapters are registered in main.dart, just open the box
    _box = await Hive.openBox<String>(_boxName);
    _controller = StreamController<bool>.broadcast();
  }

  Future<void> saveFastingDay(FastingDay day) async {
    await _safeHiveOperationAsync(() async {
      final key = day.id;
      final json = jsonEncode(day.toJson());
      await _box.put(key, json);
    });
  }

  FastingDay? getFastingDay(DateTime date) {
    return _safeHiveOperation(() {
      final key = _getKeyFromDate(date);
      final json = _box.get(key);

      if (json == null) {
        // Check for active fast from yesterday with memory safety
        return _safeHiveOperation(() {
          final yesterdayKey = _getKeyFromDate(
            date.subtract(const Duration(days: 1)),
          );
          final yesterdayJson = _box.get(yesterdayKey);

          if (yesterdayJson != null) {
            try {
              final yesterday = FastingDay.fromJson(jsonDecode(yesterdayJson));
              if (yesterday.isActive) {
                return yesterday;
              }
            } catch (e) {
              // Skip corrupted data
            }
          }
          return null;
        });
      }

      try {
        return FastingDay.fromJson(jsonDecode(json));
      } catch (e) {
        // Try to clean up corrupted data if memory allows
        _safeHiveOperation(() {
          _box.delete(key);
          return null;
        });
        return null;
      }
    });
  }

  /// Safely execute Hive operations with memory protection
  T? _safeHiveOperation<T>(T? Function() operation) {
    try {
      return operation();
    } catch (e) {
      if (e.toString().contains('Out of Memory') ||
          e.toString().contains('Exhausted heap space')) {
        // Memory exhausted - return null instead of crashing
        return null;
      }
      // Re-throw other errors
      rethrow;
    }
  }

  /// Safely execute async Hive operations with memory protection
  Future<T?> _safeHiveOperationAsync<T>(Future<T?> Function() operation) async {
    try {
      return await operation();
    } catch (e) {
      if (e.toString().contains('Out of Memory') ||
          e.toString().contains('Exhausted heap space')) {
        // Memory exhausted - return null instead of crashing
        return null;
      }
      // Re-throw other errors
      rethrow;
    }
  }

  FastingDay? getFastingDayById(String id) {
    try {
      final json = _box.get(id);
      if (json == null) return null;
      return FastingDay.fromJson(jsonDecode(json));
    } catch (e) {
      print('Error in getFastingDayById: $e');
      return null;
    }
  }

  List<FastingDay> getAllFastingDays() {
    try {
      final result = <FastingDay>[];
      for (final json in _box.values) {
        try {
          result.add(FastingDay.fromJson(jsonDecode(json)));
        } catch (e) {
          print('Error reading a fasting day: $e');
        }
      }
      return result..sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      print('Error in getAllFastingDays: $e');
      return [];
    }
  }

  List<FastingDay> getCompletedFasts() {
    try {
      final result = <FastingDay>[];
      for (final json in _box.values) {
        try {
          final day = FastingDay.fromJson(jsonDecode(json));
          if (day.completed) {
            result.add(day);
          }
        } catch (e) {
          print('Error reading a fasting day: $e');
        }
      }
      return result..sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      print('Error in getCompletedFasts: $e');
      return [];
    }
  }

  List<FastingDay> getCompletedFastingDays({int limit = 50}) {
    try {
      final result = <FastingDay>[];
      int count = 0;

      // Iterate through values and stop when we have enough completed days
      for (final json in _box.values) {
        if (count >= limit) break;

        try {
          final day = FastingDay.fromJson(jsonDecode(json));
          if (day.completed) {
            result.add(day);
            count++;
          }
        } catch (e) {
          // Log error but continue processing
          continue;
        }
      }

      return result..sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      return [];
    }
  }

  /// Get recent completed fasting days (last 30 days) for better performance
  List<FastingDay> getRecentCompletedFastingDays({int days = 30}) {
    return _safeHiveOperation(() {
          final result = <FastingDay>[];
          final cutoffDate = DateTime.now().subtract(Duration(days: days));

          // Limit the number of entries we process to prevent memory exhaustion
          int processedCount = 0;
          const maxEntries = 100; // Process at most 100 entries

          for (final json in _box.values) {
            if (processedCount >= maxEntries) break;
            processedCount++;

            try {
              final day = FastingDay.fromJson(jsonDecode(json));
              if (day.completed && day.date.isAfter(cutoffDate)) {
                result.add(day);
              }
            } catch (e) {
              // Skip corrupted entries
              continue;
            }
          }

          return result..sort((a, b) => b.date.compareTo(a.date));
        }) ??
        [];
  }

  Stream<BoxEvent> watchFastingDays() {
    return _box.watch();
  }

  Future<void> updateFastingWindow({
    required DateTime date,
    required String startTime,
    required String endTime,
    required bool isActive,
    required bool completed,
    required String fastingProtocol,
    DateTime? startDate,
    DateTime? endDate,
    int? actualFastingMinutes,
  }) async {
    await _safeHiveOperationAsync(() async {
      final key = _getKeyFromDate(date);
      final existingDay = getFastingDay(date);
      final updatedDay =
          (existingDay ??
                  FastingDay(
                    id: key,
                    date: date,
                    startTime: startTime,
                    endTime: endTime,
                    isActive: isActive,
                    completed: completed,
                    fastingProtocol: fastingProtocol,
                  ))
              .copyWith(
                startTime: startTime,
                endTime: endTime,
                isActive: isActive,
                completed: completed,
                startDate: startDate,
                endDate: endDate,
                actualFastingMinutes: actualFastingMinutes,
                fastingProtocol: fastingProtocol,
              );
      await saveFastingDay(updatedDay);
      _controller?.add(true);
      return null;
    });
  }

  Future<void> updateDailyRoutine({
    required DateTime date,
    double? waterIntake,
    int? steps,
    double? exercise,
    List<String>? achievements,
  }) async {
    final key = _getKeyFromDate(date);
    final existingDay = getFastingDay(date);
    if (existingDay != null) {
      final updatedDay = existingDay.copyWith(
        waterIntake: waterIntake ?? existingDay.waterIntake,
        steps: steps ?? existingDay.steps,
        exercise: exercise ?? existingDay.exercise,
        achievements: achievements ?? existingDay.achievements,
      );
      await saveFastingDay(updatedDay);
    }
  }

  Future<void> deleteFastingDay(FastingDay fastingDay) async {
    await _box.delete(fastingDay.id);
    _controller?.add(true);
  }

  Future<void> clearAllData() async {
    await _box.clear();
  }

  String _getKeyFromDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  Future<void> close() async {
    _isDisposed = true;
    await _controller?.close();
    _controller = null;
    if (_box.isOpen) {
      await _box.close();
    }
  }

  Future<void> addAchievement(DateTime date, String achievement) async {
    final key = _getKeyFromDate(date);
    final existingDay = getFastingDay(date);
    if (existingDay != null) {
      final currentAchievements = List<String>.from(existingDay.achievements);
      if (!currentAchievements.contains(achievement)) {
        currentAchievements.add(achievement);
        final updatedDay = existingDay.copyWith(
          achievements: currentAchievements,
        );
        await saveFastingDay(updatedDay);
      }
    }
  }
}
