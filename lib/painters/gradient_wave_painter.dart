import 'package:flutter/material.dart';
import 'dart:math';


class GradientWavePainter extends CustomPainter {
  final Animation<double> animation;
  final List<Color> colors;

  GradientWavePainter({required this.animation, required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final path = Path();
    final height = size.height;
    final width = size.width;

    // Create gradient
    paint.shader = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: colors,
    ).createShader(Rect.fromLTWH(0, 0, width, height));

    // Create wave path
    for (int i = 0; i < colors.length; i++) {
      final waveHeight = height * 0.05 * (i + 1);
      final frequency = 3.0 / width * (i + 1);
      final animationOffset = animation.value * width * 0.2;
      final verticalOffset = height * 0.1 * (i + 1);

      path.reset();
      path.moveTo(0, height * 0.8 - verticalOffset);

      for (double x = 0; x <= width; x++) {
        final y = sin((x + animationOffset) * frequency) * waveHeight +
            height * 0.8 -
            verticalOffset;
        path.lineTo(x, y);
      }

      path.lineTo(width, height);
      path.lineTo(0, height);
      path.close();

      canvas.drawPath(path, paint..color = colors[i].withOpacity(0.3));
    }
  }

  @override
  bool shouldRepaint(GradientWavePainter oldDelegate) => true;
}