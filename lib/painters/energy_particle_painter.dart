

import 'package:flutter/animation.dart';
import 'package:flutter/material.dart';
import 'dart:math';

class EnergyParticlePainter extends CustomPainter {
  final Animation<double> animation;
  final Color baseColor;

  EnergyParticlePainter({required this.animation, required this.baseColor});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 2 * 0.8;
    final random = Random(42); // Fixed seed for consistent randomness

    // Create multiple particle systems
    for (var system = 0; system < 3; system++) {
      final systemOffset = system * (2 * pi / 3);

      for (var i = 0; i < 20; i++) {
        final baseAngle = (i / 20) * 2 * pi;
        final speed = 1.0 + random.nextDouble() * 0.5;
        final angle =
            baseAngle + animation.value * speed * 2 * pi + systemOffset;

        // Create spiral motion
        final spiralRadius =
            baseRadius * (0.4 + 0.6 * sin(angle * 2 + animation.value * pi));
        final wobble = sin(angle * 8 + animation.value * 4 * pi) * 10;

        // Particle trail effect
        for (var j = 0; j < 3; j++) {
          final trailOffset = j * 0.1;
          final trailX =
              center.dx + cos(angle - trailOffset) * (spiralRadius + wobble);
          final trailY =
              center.dy + sin(angle - trailOffset) * (spiralRadius + wobble);

          final paint = Paint()
            ..color = baseColor.withOpacity(0.3 - j * 0.1)
            ..style = PaintingStyle.fill
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, 3.0 - j);

          canvas.drawCircle(
            Offset(trailX, trailY),
            3.0 - j,
            paint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(EnergyParticlePainter oldDelegate) => true;
}