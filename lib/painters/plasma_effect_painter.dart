import 'package:flutter/material.dart';
import 'package:flutter/animation.dart';
import 'dart:math';

class PlasmaEffectPainter extends CustomPainter {
  final Animation<double> animation;
  final Color baseColor;

  PlasmaEffectPainter({required this.animation, required this.baseColor});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 2 * 0.8; // Slightly smaller base radius

    // Create multiple layers of morphing plasma
    for (var layer = 0; layer < 3; layer++) {
      final path = Path();
      final points = <Offset>[];
      final numPoints = 50; // More points for smoother waves
      final layerOffset = layer * (2 * pi / 3);

      for (var i = 0; i <= numPoints; i++) {
        final angle = (i / numPoints) * 2 * pi;
        final wavePhase = animation.value * 2 * pi + layerOffset;

        // Create complex wave pattern
        final radiusNoise = sin(angle * 3 + wavePhase) * 0.2 +
            cos(angle * 5 - wavePhase) * 0.1 +
            sin(angle * 7 + wavePhase) * 0.05;

        final radius = baseRadius * (1 + radiusNoise);

        final x = center.dx + cos(angle) * radius;
        final y = center.dy + sin(angle) * radius;

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          // Use quadratic bezier curves for smoother waves
          final prevPoint = points.last;
          final midPoint = Offset(
            (prevPoint.dx + x) / 2,
            (prevPoint.dy + y) / 2,
          );
          path.quadraticBezierTo(
              prevPoint.dx, prevPoint.dy, midPoint.dx, midPoint.dy);
        }

        points.add(Offset(x, y));
      }

      // Close the path smoothly
      final firstPoint = points.first;
      final lastPoint = points.last;
      final midPoint = Offset(
        (firstPoint.dx + lastPoint.dx) / 2,
        (firstPoint.dy + lastPoint.dy) / 2,
      );
      path.quadraticBezierTo(
          lastPoint.dx, lastPoint.dy, midPoint.dx, midPoint.dy);

      // Create gradient paint with dynamic colors
      final paint = Paint()
        ..shader = RadialGradient(
          colors: [
            baseColor.withOpacity(0.3 - layer * 0.1),
            baseColor.withOpacity(0.1 - layer * 0.03),
            Colors.transparent,
          ],
          stops: [0.2, 0.7, 1.0],
        ).createShader(Rect.fromCircle(center: center, radius: baseRadius))
        ..style = PaintingStyle.fill
        ..blendMode = BlendMode.screen;

      canvas.drawPath(path, paint);

      // Add glow effect
      final glowPaint = Paint()
        ..color = baseColor.withOpacity(0.1)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0
        ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 8);

      canvas.drawPath(path, glowPaint);
    }
  }

  @override
  bool shouldRepaint(PlasmaEffectPainter oldDelegate) => true;
}

class OptimizedPlasmaEffectPainter extends CustomPainter {
  final Animation<double> animation;
  final Color baseColor;

  // Cache paint objects to avoid recreation
  static final Paint _paint = Paint()..style = PaintingStyle.fill;

  OptimizedPlasmaEffectPainter({
    required this.animation,
    required this.baseColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Only paint if visible and animation is running
    if (size.width <= 0 || size.height <= 0) return;

    // Reduce complexity - use fewer plasma points
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final animValue = animation.value;

    // Simplified plasma effect with fewer calculations
    _paint.color = baseColor.withOpacity(0.3 + 0.2 * sin(animValue * 2 * pi));

    // Draw fewer, larger circles instead of complex plasma
    for (int i = 0; i < 3; i++) { // Reduced from more complex calculations
      final offset = Offset(
        center.dx + cos(animValue * 2 * pi + i) * radius * 0.3,
        center.dy + sin(animValue * 2 * pi + i) * radius * 0.3,
      );
      canvas.drawCircle(offset, radius * 0.2, _paint);
    }
  }

  @override
  bool shouldRepaint(OptimizedPlasmaEffectPainter oldDelegate) {
    // Only repaint if animation value changed significantly
    return (animation.value - oldDelegate.animation.value).abs() > 0.01;
  }
}