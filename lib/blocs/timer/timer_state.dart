import 'package:equatable/equatable.dart';

enum TimerStatus { initial, running, paused, completed }

class TimerState extends Equatable {
  final int duration;
  final TimerStatus status;
  final int remainingTime;

  const TimerState({
    required this.duration,
    required this.status,
    required this.remainingTime,
  });

  factory TimerState.initial() {
    return const TimerState(
      duration: 0,
      status: TimerStatus.initial,
      remainingTime: 0,
    );
  }

  TimerState copyWith({
    int? duration,
    TimerStatus? status,
    int? remainingTime,
  }) {
    return TimerState(
      duration: duration ?? this.duration,
      status: status ?? this.status,
      remainingTime: remainingTime ?? this.remainingTime,
    );
  }

  @override
  List<Object?> get props => [duration, status, remainingTime];
}