import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:async';
import 'auth_event.dart';
import 'auth_state.dart';
import '../../services/auth_persistence_service.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  StreamSubscription<User?>? _authStateSubscription;

  AuthBloc() : super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<SignInWithEmailPasswordRequested>(_onSignInWithEmailPasswordRequested);
    on<SignUpWithEmailPasswordRequested>(_onSignUpWithEmailPasswordRequested);
    on<SignInWithGoogleRequested>(_onSignInWithGoogleRequested);
    on<SignOutRequested>(_onSignOutRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // Check if Firebase user exists (Firebase handles session persistence automatically)
      final user = _firebaseAuth.currentUser;

      if (user != null) {
        // User is authenticated, verify and sync stored data
        final isDataValid =
            await AuthPersistenceService.verifyUserDataIntegrity(user);
        final isSessionValid = await AuthPersistenceService.isSessionValid();

        if (isDataValid && isSessionValid) {
          // Sync any changes from Firebase
          await AuthPersistenceService.syncWithFirebaseUser(user);

          // Get stored data for display
          final storedData = await AuthPersistenceService.getStoredUserData();
          emit(
            Authenticated(
              userId: user.uid,
              email: storedData?['email'] ?? user.email,
              displayName: storedData?['displayName'] ?? user.displayName,
            ),
          );
        } else {
          // Refresh stored data
          await AuthPersistenceService.persistUserData(user);
          emit(
            Authenticated(
              userId: user.uid,
              email: user.email,
              displayName: user.displayName,
            ),
          );
        }
      } else {
        // No authenticated user found
        await AuthPersistenceService.clearAuthData();
        emit(Unauthenticated());
      }
    } catch (e) {
      // If there's an error checking auth state, assume unauthenticated
      await AuthPersistenceService.clearAuthData();
      emit(Unauthenticated());
    }
  }

  Future<void> _onSignInWithEmailPasswordRequested(
    SignInWithEmailPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );
      final user = userCredential.user;
      if (user != null) {
        await AuthPersistenceService.persistUserData(
          user,
          authMethod: 'email_password',
        );
        emit(
          Authenticated(
            userId: user.uid,
            email: user.email,
            displayName: user.displayName,
          ),
        );
      } else {
        emit(const AuthFailure('No user found'));
      }
    } on FirebaseAuthException catch (e) {
      emit(
        AuthFailure(e.message ?? 'Failed to sign in with email and password'),
      );
    } catch (e) {
      emit(const AuthFailure('Failed to sign in with email and password'));
    }
  }

  Future<void> _onSignUpWithEmailPasswordRequested(
    SignUpWithEmailPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );
      final user = userCredential.user;
      if (user != null) {
        // Update display name if provided during sign up
        if (event.fullName.isNotEmpty) {
          await user.updateDisplayName(event.fullName);
          await user.reload(); // Reload to get updated user data
        }

        await AuthPersistenceService.persistUserData(
          user,
          authMethod: 'email_password',
        );
        emit(
          Authenticated(
            userId: user.uid,
            email: user.email,
            displayName: user.displayName ?? event.fullName,
          ),
        );
      } else {
        emit(const AuthFailure('No user found'));
      }
    } on FirebaseAuthException catch (e) {
      emit(
        AuthFailure(e.message ?? 'Failed to sign up with email and password'),
      );
    } catch (e) {
      emit(const AuthFailure('Failed to sign up with email and password'));
    }
  }

  Future<void> _onSignInWithGoogleRequested(
    SignInWithGoogleRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      // For now, disable Google Sign-In until proper migration is completed
      // This prevents crashes while maintaining app functionality
      emit(
        const AuthFailure(
          'Google Sign-In is temporarily disabled. Please use email/password authentication.',
        ),
      );
      return;

      // TODO: Implement Google Sign-In 7.0+ API
      // The new API requires significant changes and proper platform configuration
      // This should be implemented as a separate task with proper testing
    } on FirebaseAuthException catch (e) {
      emit(AuthFailure(e.message ?? 'Failed to sign in with Google'));
    } catch (e) {
      emit(AuthFailure('Failed to sign in with Google: ${e.toString()}'));
    }
  }

  Future<void> _onSignOutRequested(
    SignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
      await AuthPersistenceService.clearAuthData();
      emit(Unauthenticated());
    } catch (e) {
      emit(const AuthFailure('Failed to sign out'));
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
