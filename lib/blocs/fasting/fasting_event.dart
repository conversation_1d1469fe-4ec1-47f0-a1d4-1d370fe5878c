import 'package:equatable/equatable.dart';
import '../../models/fasting_day.dart';

abstract class FastingEvent extends Equatable {
  const FastingEvent();

  @override
  List<Object?> get props => [];
}

// Load current fasting day
class LoadCurrentFastingDay extends FastingEvent {
  final DateTime? date;

  const LoadCurrentFastingDay({this.date});

  @override
  List<Object?> get props => [date];
}

// Start a new fast
class StartFast extends FastingEvent {
  final DateTime startDate;
  final String fastingProtocol;

  const StartFast({
    required this.startDate,
    required this.fastingProtocol,
  });

  @override
  List<Object?> get props => [startDate, fastingProtocol];
}

// End current fast
class EndFast extends FastingEvent {
  final DateTime endDate;

  const EndFast({required this.endDate});

  @override
  List<Object?> get props => [endDate];
}

// Update fasting progress (for timer updates)
class UpdateFastingProgress extends FastingEvent {
  final DateTime currentTime;

  const UpdateFastingProgress({required this.currentTime});

  @override
  List<Object?> get props => [currentTime];
}

// Load fasting history with pagination
class LoadFastingHistory extends FastingEvent {
  final int page;
  final int limit;
  final bool refresh;

  const LoadFastingHistory({
    this.page = 0,
    this.limit = 20,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, limit, refresh];
}

// Update daily routine data
class UpdateDailyRoutine extends FastingEvent {
  final DateTime date;
  final double? waterIntake;
  final int? steps;
  final double? exercise;

  const UpdateDailyRoutine({
    required this.date,
    this.waterIntake,
    this.steps,
    this.exercise,
  });

  @override
  List<Object?> get props => [date, waterIntake, steps, exercise];
}

// Save fasting day
class SaveFastingDay extends FastingEvent {
  final FastingDay fastingDay;

  const SaveFastingDay({required this.fastingDay});

  @override
  List<Object?> get props => [fastingDay];
}

// Delete fasting day
class DeleteFastingDay extends FastingEvent {
  final String fastingDayId;

  const DeleteFastingDay({required this.fastingDayId});

  @override
  List<Object?> get props => [fastingDayId];
}

// Load fasting statistics
class LoadFastingStats extends FastingEvent {
  final int days;

  const LoadFastingStats({this.days = 30});

  @override
  List<Object?> get props => [days];
}
