import 'package:equatable/equatable.dart';
import '../../models/fasting_day.dart';

class FastingState extends Equatable {
  final FastingDay? currentFastingDay;
  final List<FastingDay> recentHistory;
  final FastingStats? stats;
  final bool isLoading;
  final bool isLoadingHistory;
  final String? error;
  final int currentHistoryPage;
  final bool hasMoreHistory;
  final DateTime? lastUpdated;

  const FastingState({
    this.currentFastingDay,
    this.recentHistory = const [],
    this.stats,
    this.isLoading = false,
    this.isLoadingHistory = false,
    this.error,
    this.currentHistoryPage = 0,
    this.hasMoreHistory = true,
    this.lastUpdated,
  });

  FastingState copyWith({
    FastingDay? currentFastingDay,
    List<FastingDay>? recentHistory,
    FastingStats? stats,
    bool? isLoading,
    bool? isLoadingHistory,
    String? error,
    int? currentHistoryPage,
    bool? hasMoreHistory,
    DateTime? lastUpdated,
  }) {
    return FastingState(
      currentFastingDay: currentFastingDay ?? this.currentFastingDay,
      recentHistory: recentHistory ?? this.recentHistory,
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      isLoadingHistory: isLoadingHistory ?? this.isLoadingHistory,
      error: error,
      currentHistoryPage: currentHistoryPage ?? this.currentHistoryPage,
      hasMoreHistory: hasMoreHistory ?? this.hasMoreHistory,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  List<Object?> get props => [
        currentFastingDay,
        recentHistory,
        stats,
        isLoading,
        isLoadingHistory,
        error,
        currentHistoryPage,
        hasMoreHistory,
        lastUpdated,
      ];
}

class FastingStats extends Equatable {
  final int totalFasts;
  final int completedFasts;
  final int currentStreak;
  final int longestStreak;
  final double averageFastingHours;
  final double totalFastingHours;
  final DateTime? lastFastDate;

  const FastingStats({
    required this.totalFasts,
    required this.completedFasts,
    required this.currentStreak,
    required this.longestStreak,
    required this.averageFastingHours,
    required this.totalFastingHours,
    this.lastFastDate,
  });

  @override
  List<Object?> get props => [
        totalFasts,
        completedFasts,
        currentStreak,
        longestStreak,
        averageFastingHours,
        totalFastingHours,
        lastFastDate,
      ];
}
