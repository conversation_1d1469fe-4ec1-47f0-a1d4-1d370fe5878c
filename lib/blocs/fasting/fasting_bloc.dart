import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/fasting_service.dart';
import '../../models/fasting_day.dart';
import 'fasting_event.dart';
import 'fasting_state.dart';

class FastingBloc extends Bloc<FastingEvent, FastingState> {
  final FastingService _fastingService;
  StreamSubscription? _fastingSubscription;
  Timer? _progressTimer;

  // Memory management
  static const int _maxHistoryItems = 50; // Limit in-memory history

  FastingBloc(this._fastingService) : super(const FastingState()) {
    on<LoadCurrentFastingDay>(_onLoadCurrentFastingDay);
    on<StartFast>(_onStartFast);
    on<EndFast>(_onEndFast);
    on<UpdateFastingProgress>(_onUpdateFastingProgress);
    on<LoadFastingHistory>(_onLoadFastingHistory);
    on<UpdateDailyRoutine>(_onUpdateDailyRoutine);
    on<SaveFastingDay>(_onSaveFastingDay);
    on<DeleteFastingDay>(_onDeleteFastingDay);
    on<LoadFastingStats>(_onLoadFastingStats);

    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _fastingService.init();
      _setupDataListener();
      add(const LoadCurrentFastingDay());
      add(const LoadFastingStats());
    } catch (e) {
      // Handle initialization errors by adding an event instead of direct emit
      add(const LoadCurrentFastingDay());
    }
  }

  void _setupDataListener() {
    try {
      _fastingSubscription = _fastingService.watchFastingDays().listen(
        (_) {
          // Reload current day when data changes
          add(const LoadCurrentFastingDay());
        },
        onError: (error) {
          // Handle errors by reloading data instead of direct emit
          add(const LoadCurrentFastingDay());
        },
      );
    } catch (e) {
      // Handle subscription setup errors gracefully
    }
  }

  Future<void> _onLoadCurrentFastingDay(
    LoadCurrentFastingDay event,
    Emitter<FastingState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final date = event.date ?? DateTime.now();
      final fastingDay = _fastingService.getFastingDay(date);

      emit(
        state.copyWith(
          currentFastingDay: fastingDay,
          isLoading: false,
          lastUpdated: DateTime.now(),
        ),
      );

      // Start progress timer if fast is active
      if (fastingDay?.isActive == true) {
        _startProgressTimer();
      } else {
        _stopProgressTimer();
      }
    } catch (e) {
      emit(
        state.copyWith(
          error: 'Failed to load current fasting day: $e',
          isLoading: false,
        ),
      );
    }
  }

  Future<void> _onStartFast(StartFast event, Emitter<FastingState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final fastingDay = FastingDay(
        id: _generateId(event.startDate),
        date: event.startDate,
        startTime: _formatTime(event.startDate),
        endTime: '', // Will be set when fast ends
        isActive: true,
        completed: false,
        startDate: event.startDate,
        fastingProtocol: event.fastingProtocol,
        waterIntake: 0.0,
        steps: 0,
        exercise: 0.0,
        achievements: [],
      );

      await _fastingService.saveFastingDay(fastingDay);

      emit(
        state.copyWith(
          currentFastingDay: fastingDay,
          isLoading: false,
          lastUpdated: DateTime.now(),
        ),
      );

      _startProgressTimer();
    } catch (e) {
      emit(state.copyWith(error: 'Failed to start fast: $e', isLoading: false));
    }
  }

  Future<void> _onEndFast(EndFast event, Emitter<FastingState> emit) async {
    try {
      if (state.currentFastingDay == null ||
          !state.currentFastingDay!.isActive) {
        emit(state.copyWith(error: 'No active fast to end'));
        return;
      }

      emit(state.copyWith(isLoading: true, error: null));

      final currentFast = state.currentFastingDay!;
      final fastingMinutes = event.endDate
          .difference(currentFast.startDate!)
          .inMinutes;

      final updatedFast = currentFast.copyWith(
        endTime: _formatTime(event.endDate),
        endDate: event.endDate,
        isActive: false,
        completed: true,
        actualFastingMinutes: fastingMinutes,
      );

      await _fastingService.saveFastingDay(updatedFast);

      emit(
        state.copyWith(
          currentFastingDay: updatedFast,
          isLoading: false,
          lastUpdated: DateTime.now(),
        ),
      );

      _stopProgressTimer();

      // Reload stats after completing a fast
      add(const LoadFastingStats());
    } catch (e) {
      emit(state.copyWith(error: 'Failed to end fast: $e', isLoading: false));
    }
  }

  void _onUpdateFastingProgress(
    UpdateFastingProgress event,
    Emitter<FastingState> emit,
  ) {
    if (state.currentFastingDay?.isActive == true) {
      // Just emit the current state with updated timestamp
      // This triggers UI updates without heavy operations
      emit(state.copyWith(lastUpdated: event.currentTime));
    }
  }

  Future<void> _onLoadFastingHistory(
    LoadFastingHistory event,
    Emitter<FastingState> emit,
  ) async {
    try {
      if (event.refresh) {
        emit(
          state.copyWith(
            isLoadingHistory: true,
            recentHistory: [],
            currentHistoryPage: 0,
            hasMoreHistory: true,
          ),
        );
      } else if (!state.hasMoreHistory || state.isLoadingHistory) {
        return;
      } else {
        emit(state.copyWith(isLoadingHistory: true));
      }

      // Use memory-efficient pagination
      final history = _fastingService.getCompletedFastingDays(
        limit: event.limit,
      );

      // Limit total items in memory
      final newHistory = event.refresh
          ? history
          : [...state.recentHistory, ...history];

      final limitedHistory = newHistory.length > _maxHistoryItems
          ? newHistory.take(_maxHistoryItems).toList()
          : newHistory;

      emit(
        state.copyWith(
          recentHistory: limitedHistory,
          isLoadingHistory: false,
          currentHistoryPage: event.page,
          hasMoreHistory: history.length >= event.limit,
          error: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          error: 'Failed to load fasting history: $e',
          isLoadingHistory: false,
        ),
      );
    }
  }

  Future<void> _onUpdateDailyRoutine(
    UpdateDailyRoutine event,
    Emitter<FastingState> emit,
  ) async {
    try {
      await _fastingService.updateDailyRoutine(
        date: event.date,
        waterIntake: event.waterIntake,
        steps: event.steps,
        exercise: event.exercise,
      );

      // Reload current day if it's today
      if (_isSameDay(event.date, DateTime.now())) {
        add(const LoadCurrentFastingDay());
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update daily routine: $e'));
    }
  }

  Future<void> _onSaveFastingDay(
    SaveFastingDay event,
    Emitter<FastingState> emit,
  ) async {
    try {
      await _fastingService.saveFastingDay(event.fastingDay);

      // Update current day if it matches
      if (_isSameDay(event.fastingDay.date, DateTime.now())) {
        emit(
          state.copyWith(
            currentFastingDay: event.fastingDay,
            lastUpdated: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to save fasting day: $e'));
    }
  }

  Future<void> _onDeleteFastingDay(
    DeleteFastingDay event,
    Emitter<FastingState> emit,
  ) async {
    try {
      final fastingDay = _fastingService.getFastingDayById(event.fastingDayId);
      if (fastingDay != null) {
        await _fastingService.deleteFastingDay(fastingDay);

        // Remove from history and reload stats
        final updatedHistory = state.recentHistory
            .where((day) => day.id != event.fastingDayId)
            .toList();

        emit(state.copyWith(recentHistory: updatedHistory));
        add(const LoadFastingStats());
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to delete fasting day: $e'));
    }
  }

  Future<void> _onLoadFastingStats(
    LoadFastingStats event,
    Emitter<FastingState> emit,
  ) async {
    try {
      // Use memory-efficient method for stats calculation
      final recentFasts = _fastingService.getRecentCompletedFastingDays(
        days: event.days,
      );

      final stats = _calculateStats(recentFasts);

      emit(state.copyWith(stats: stats));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to load fasting stats: $e'));
    }
  }

  FastingStats _calculateStats(List<FastingDay> fasts) {
    if (fasts.isEmpty) {
      return const FastingStats(
        totalFasts: 0,
        completedFasts: 0,
        currentStreak: 0,
        longestStreak: 0,
        averageFastingHours: 0.0,
        totalFastingHours: 0.0,
      );
    }

    final completedFasts = fasts.where((f) => f.completed).toList();
    final totalMinutes = completedFasts
        .map((f) => f.actualFastingMinutes ?? 0)
        .fold(0, (a, b) => a + b);

    return FastingStats(
      totalFasts: fasts.length,
      completedFasts: completedFasts.length,
      currentStreak: _calculateCurrentStreak(fasts),
      longestStreak: _calculateLongestStreak(fasts),
      averageFastingHours: completedFasts.isNotEmpty
          ? totalMinutes / completedFasts.length / 60.0
          : 0.0,
      totalFastingHours: totalMinutes / 60.0,
      lastFastDate: completedFasts.isNotEmpty
          ? completedFasts.first.endDate
          : null,
    );
  }

  int _calculateCurrentStreak(List<FastingDay> fasts) {
    // Implementation for current streak calculation
    // This is a simplified version - you can enhance it
    int streak = 0;
    final now = DateTime.now();

    for (final fast in fasts) {
      if (fast.completed && now.difference(fast.date).inDays <= streak + 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  int _calculateLongestStreak(List<FastingDay> fasts) {
    // Implementation for longest streak calculation
    // This is a simplified version - you can enhance it
    int maxStreak = 0;
    int currentStreak = 0;

    for (final fast in fasts.reversed) {
      if (fast.completed) {
        currentStreak++;
        maxStreak = currentStreak > maxStreak ? currentStreak : maxStreak;
      } else {
        currentStreak = 0;
      }
    }

    return maxStreak;
  }

  void _startProgressTimer() {
    _stopProgressTimer();
    _progressTimer = Timer.periodic(
      const Duration(minutes: 1), // Update every minute to save battery
      (timer) {
        add(UpdateFastingProgress(currentTime: DateTime.now()));
      },
    );
  }

  void _stopProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  String _generateId(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  @override
  Future<void> close() {
    _fastingSubscription?.cancel();
    _stopProgressTimer();
    return super.close();
  }
}
