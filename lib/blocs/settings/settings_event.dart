import 'package:equatable/equatable.dart';

abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadSettings extends SettingsEvent {}

class UpdateNotifications extends SettingsEvent {
  final bool enabled;
  const UpdateNotifications(this.enabled);

  @override
  List<Object?> get props => [enabled];
}

class UpdateDarkTheme extends SettingsEvent {
  final bool enabled;
  const UpdateDarkTheme(this.enabled);

  @override
  List<Object?> get props => [enabled];
}

class UpdateFastingProtocol extends SettingsEvent {
  final String protocol;
  const UpdateFastingProtocol(this.protocol);

  @override
  List<Object?> get props => [protocol];
}

class UpdateFastingStartTime extends SettingsEvent {
  final int hour;
  final int minute;
  const UpdateFastingStartTime(this.hour, this.minute);

  @override
  List<Object?> get props => [hour, minute];
}

class UpdateMetricSystem extends SettingsEvent {
  final bool useMetric;
  const UpdateMetricSystem(this.useMetric);

  @override
  List<Object?> get props => [useMetric];
}

class UpdateMotivationalQuotes extends SettingsEvent {
  final bool show;
  const UpdateMotivationalQuotes(this.show);

  @override
  List<Object?> get props => [show];
}

class SaveAllSettings extends SettingsEvent {}