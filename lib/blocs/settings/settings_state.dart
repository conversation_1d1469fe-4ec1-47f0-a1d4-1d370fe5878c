import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class SettingsState extends Equatable {
  final bool notificationsEnabled;
  final bool darkThemeEnabled;
  final String fastingProtocol;
  final int fastingStartHour;
  final int fastingStartMinute;
  final bool useMetricSystem;
  final bool showMotivationalQuotes;
  final bool isLoading;
  final String? error;

  TimeOfDay get fastingStartTime => TimeOfDay(
        hour: fastingStartHour,
        minute: fastingStartMinute,
      );

  const SettingsState({
    this.notificationsEnabled = true,
    this.darkThemeEnabled = true,
    this.fastingProtocol = '16:8',
    this.fastingStartHour = 20,
    this.fastingStartMinute = 0,
    this.useMetricSystem = true,
    this.showMotivationalQuotes = true,
    this.isLoading = false,
    this.error,
  });

  SettingsState copyWith({
    bool? notificationsEnabled,
    bool? darkThemeEnabled,
    String? fastingProtocol,
    int? fastingStartHour,
    int? fastingStartMinute,
    bool? useMetricSystem,
    bool? showMotivationalQuotes,
    bool? isLoading,
    String? error,
  }) {
    return SettingsState(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      darkThemeEnabled: darkThemeEnabled ?? this.darkThemeEnabled,
      fastingProtocol: fastingProtocol ?? this.fastingProtocol,
      fastingStartHour: fastingStartHour ?? this.fastingStartHour,
      fastingStartMinute: fastingStartMinute ?? this.fastingStartMinute,
      useMetricSystem: useMetricSystem ?? this.useMetricSystem,
      showMotivationalQuotes: showMotivationalQuotes ?? this.showMotivationalQuotes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [
        notificationsEnabled,
        darkThemeEnabled,
        fastingProtocol,
        fastingStartHour,
        fastingStartMinute,
        useMetricSystem,
        showMotivationalQuotes,
        isLoading,
        error,
      ];
}