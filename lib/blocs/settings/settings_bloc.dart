import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'settings_event.dart';
import 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  SettingsBloc() : super(const SettingsState()) {
    on<LoadSettings>(_onLoadSettings);
    on<UpdateNotifications>(_onUpdateNotifications);
    on<UpdateDarkTheme>(_onUpdateDarkTheme);
    on<UpdateFastingProtocol>(_onUpdateFastingProtocol);
    on<UpdateFastingStartTime>(_onUpdateFastingStartTime);
    on<UpdateMetricSystem>(_onUpdateMetricSystem);
    on<UpdateMotivationalQuotes>(_onUpdateMotivationalQuotes);
    on<SaveAllSettings>(_onSaveAllSettings);
  }

  Future<void> _onLoadSettings(
      LoadSettings event, Emitter<SettingsState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      final prefs = await SharedPreferences.getInstance();

      emit(state.copyWith(
        notificationsEnabled: prefs.getBool('notifications_enabled') ?? true,
        darkThemeEnabled: prefs.getBool('dark_theme_enabled') ?? true,
        fastingProtocol: prefs.getString('fasting_protocol') ?? '16:8',
        fastingStartHour: prefs.getInt('fasting_start_hour') ?? 20,
        fastingStartMinute: prefs.getInt('fasting_start_minute') ?? 0,
        useMetricSystem: prefs.getBool('use_metric_system') ?? true,
        showMotivationalQuotes: prefs.getBool('show_motivational_quotes') ?? true,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  void _onUpdateNotifications(
      UpdateNotifications event, Emitter<SettingsState> emit) {
    emit(state.copyWith(notificationsEnabled: event.enabled));
  }

  void _onUpdateDarkTheme(UpdateDarkTheme event, Emitter<SettingsState> emit) {
    emit(state.copyWith(darkThemeEnabled: event.enabled));
  }

  void _onUpdateFastingProtocol(
      UpdateFastingProtocol event, Emitter<SettingsState> emit) {
    emit(state.copyWith(fastingProtocol: event.protocol));
  }

  void _onUpdateFastingStartTime(
      UpdateFastingStartTime event, Emitter<SettingsState> emit) {
    emit(state.copyWith(
      fastingStartHour: event.hour,
      fastingStartMinute: event.minute,
    ));
  }

  void _onUpdateMetricSystem(
      UpdateMetricSystem event, Emitter<SettingsState> emit) {
    emit(state.copyWith(useMetricSystem: event.useMetric));
  }

  void _onUpdateMotivationalQuotes(
      UpdateMotivationalQuotes event, Emitter<SettingsState> emit) {
    emit(state.copyWith(showMotivationalQuotes: event.show));
  }

  Future<void> _onSaveAllSettings(
      SaveAllSettings event, Emitter<SettingsState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('notifications_enabled', state.notificationsEnabled);
      await prefs.setBool('dark_theme_enabled', state.darkThemeEnabled);
      await prefs.setString('fasting_protocol', state.fastingProtocol);
      await prefs.setInt('fasting_start_hour', state.fastingStartHour);
      await prefs.setInt('fasting_start_minute', state.fastingStartMinute);
      await prefs.setBool('use_metric_system', state.useMetricSystem);
      await prefs.setBool('show_motivational_quotes', state.showMotivationalQuotes);

      emit(state.copyWith(isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }
}