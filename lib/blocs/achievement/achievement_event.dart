import 'package:equatable/equatable.dart';
import '../../models/achievement.dart';

abstract class AchievementEvent extends Equatable {
  const AchievementEvent();

  @override
  List<Object?> get props => [];
}

class LoadAchievements extends AchievementEvent {}

class CheckForAchievements extends AchievementEvent {}

class AchievementUnlocked extends AchievementEvent {
  final Achievement achievement;
  
  const AchievementUnlocked(this.achievement);
  
  @override
  List<Object?> get props => [achievement];
}