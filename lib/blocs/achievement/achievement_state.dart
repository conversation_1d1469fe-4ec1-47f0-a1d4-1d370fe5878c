import 'package:equatable/equatable.dart';
import '../../models/achievement.dart';

class AchievementState extends Equatable {
  final List<Achievement> achievements;
  final List<Achievement> lockedAchievements;
  final int totalPoints;
  final List<Achievement> recentUnlocks;
  final bool isLoading;

  const AchievementState({
    this.achievements = const [],
    this.lockedAchievements = const [],
    this.totalPoints = 0,
    this.recentUnlocks = const [],
    this.isLoading = false,
  });

  AchievementState copyWith({
    List<Achievement>? achievements,
    List<Achievement>? lockedAchievements,
    int? totalPoints,
    List<Achievement>? recentUnlocks,
    bool? isLoading,
  }) {
    return AchievementState(
      achievements: achievements ?? this.achievements,
      lockedAchievements: lockedAchievements ?? this.lockedAchievements,
      totalPoints: totalPoints ?? this.totalPoints,
      recentUnlocks: recentUnlocks ?? this.recentUnlocks,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props =>
      [achievements, lockedAchievements, totalPoints, recentUnlocks, isLoading];
}
