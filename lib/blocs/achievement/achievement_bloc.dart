import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/achievement.dart';
import '../../services/achievement_service.dart';
import '../../services/fasting_service.dart';
import 'achievement_event.dart';
import 'achievement_state.dart';

class AchievementBloc extends Bloc<AchievementEvent, AchievementState> {
  final AchievementService _achievementService;
  final FastingService _fastingService;

  AchievementBloc(this._achievementService, this._fastingService)
      : super(const AchievementState()) {
    on<LoadAchievements>(_onLoadAchievements);
    on<CheckForAchievements>(_onCheckForAchievements);
    on<AchievementUnlocked>(_onAchievementUnlocked);
  }

  void _onLoadAchievements(
      LoadAchievements event, Emitter<AchievementState> emit) {
    emit(state.copyWith(isLoading: true));

    final achievements = _achievementService.getAllAchievements();

    // Get all possible achievements and filter out the unlocked ones to get locked ones
    final allPossibleAchievements =
        _achievementService.getAllPossibleAchievements();
    final unlockedIds = achievements.map((a) => a.id).toSet();
    final lockedAchievements = allPossibleAchievements
        .where((a) => !unlockedIds.contains(a.id))
        .toList();

    // Calculate total points
    final totalPoints =
        achievements.fold(0, (sum, achievement) => sum + achievement.points);

    emit(state.copyWith(
      achievements: achievements,
      lockedAchievements: lockedAchievements,
      totalPoints: totalPoints,
      isLoading: false,
    ));
  }

  Future<void> _onCheckForAchievements(
      CheckForAchievements event, Emitter<AchievementState> emit) async {
    final completedFasts = _fastingService.getCompletedFastingDays();

    final newAchievements =
        await _achievementService.checkForNewAchievements(completedFasts);

    if (newAchievements.isNotEmpty) {
      for (final achievement in newAchievements) {
        add(AchievementUnlocked(achievement));
      }
    }
  }

  void _onAchievementUnlocked(
      AchievementUnlocked event, Emitter<AchievementState> emit) {
    final currentUnlocks = List<Achievement>.from(state.recentUnlocks);
    currentUnlocks.add(event.achievement);

    final achievements = _achievementService.getAllAchievements();
    final totalPoints = _achievementService.getTotalPoints();

    emit(state.copyWith(
      achievements: achievements,
      totalPoints: totalPoints,
      recentUnlocks: currentUnlocks,
    ));
  }
}
