import 'package:equatable/equatable.dart';
import '../../models/body_metric.dart';

abstract class BodyMetricsEvent extends Equatable {
  const BodyMetricsEvent();

  @override
  List<Object?> get props => [];
}

class LoadBodyMetrics extends BodyMetricsEvent {}

class AddBodyMetric extends BodyMetricsEvent {
  final double weight;
  final double height;
  final DateTime date;

  const AddBodyMetric({
    required this.weight,
    required this.height,
    required this.date,
  });

  @override
  List<Object?> get props => [weight, height, date];
}

class UpdateBodyMetric extends BodyMetricsEvent {
  final DateTime originalDate;
  final DateTime date;
  final double weight;
  final double height;

  const UpdateBodyMetric({
    required this.originalDate,
    required this.date,
    required this.weight,
    required this.height,
  });

  @override
  List<Object?> get props => [originalDate, date, weight, height];
}

class DeleteBodyMetric extends BodyMetricsEvent {
  final DateTime date;

  const DeleteBodyMetric(BodyMetric metric, {required this.date});

  @override
  List<Object?> get props => [date];
}
