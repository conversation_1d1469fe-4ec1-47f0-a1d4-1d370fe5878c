import '../../models/body_metric.dart';

class BodyMetricsState {
  final List<BodyMetric> metrics;
  final bool isLoading;
  final String? error;

  const BodyMetricsState({
    this.metrics = const [],
    this.isLoading = false,
    this.error,
  });

  BodyMetricsState copyWith({
    List<BodyMetric>? metrics,
    bool? isLoading,
    String? error,
  }) {
    return BodyMetricsState(
      metrics: metrics ?? this.metrics,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  String toString() => 'BodyMetricsState(metrics: ${metrics.length}, isLoading: $isLoading, error: $error)';
}
