import 'dart:async';

import 'package:fasttime/blocs/body_metrics/body_metrics_event.dart';
import 'package:fasttime/blocs/body_metrics/body_metrics_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/body_metrics_service.dart';
import '../../models/body_metric.dart';

// BLoC
class BodyMetricsBloc extends Bloc<BodyMetricsEvent, BodyMetricsState> {
  final BodyMetricsService _metricsService;
  StreamSubscription? _metricsSubscription;

  BodyMetricsBloc(this._metricsService)
      : super(BodyMetricsState(
          metrics: [],
          isLoading: false,
        )) {
    on<LoadBodyMetrics>(_onLoadBodyMetrics);
    on<AddBodyMetric>(_onAddBodyMetric);
    on<DeleteBodyMetric>(_onDeleteBodyMetric);
    on<UpdateBodyMetric>(_onUpdateBodyMetric);

    // Initialize service and setup data listener
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _metricsService.init();
      _setupDataListener();
      add(LoadBodyMetrics());
    } catch (e) {
      print('Error initializing BodyMetricsBloc: $e');
      // Still try to load metrics even if initialization fails
      add(LoadBodyMetrics());
    }
  }

  void _setupDataListener() {
    try {
      _metricsSubscription = _metricsService.watchMetrics().listen((_) {
        add(LoadBodyMetrics());
      }, onError: (error) {
        print('Error in metrics subscription: $error');
        // Try to reload metrics if there's an error
        add(LoadBodyMetrics());
      });
    } catch (e) {
      print('Error setting up data listener: $e');
    }
  }

  Future<void> _onLoadBodyMetrics(
    LoadBodyMetrics event,
    Emitter<BodyMetricsState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final metrics = _metricsService.getAllMeasurements();
      emit(state.copyWith(
        metrics: metrics,
        isLoading: false,
      ));
    } catch (e) {
      print('Error loading body metrics: $e');
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> _onAddBodyMetric(
    AddBodyMetric event,
    Emitter<BodyMetricsState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true));

      final metric = BodyMetric(
        date: event.date,
        weight: event.weight,
        height: event.height,
      );

      await _metricsService.addMeasurement(metric);
      // No need to manually load metrics here as the watch stream will trigger
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> _onDeleteBodyMetric(
    DeleteBodyMetric event,
    Emitter<BodyMetricsState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true));
      await _metricsService.deleteMeasurement(event.date);
      // No need to manually load metrics here as the watch stream will trigger
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> _onUpdateBodyMetric(
    UpdateBodyMetric event,
    Emitter<BodyMetricsState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true));
      await _metricsService.deleteMeasurement(event.originalDate);
      final metric = BodyMetric(
        date: event.date,
        weight: event.weight,
        height: event.height,
      );
      await _metricsService.addMeasurement(metric);
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  @override
  Future<void> close() {
    _metricsSubscription?.cancel();
    return super.close();
  }

  // Add this method to diagnose issues
  Future<Map<String, dynamic>> diagnoseIssues() async {
    try {
      // Await initialization
      await _metricsService.init();
      final isBoxOpen = _metricsService.isBoxOpen();
      final metrics = _metricsService.getAllMeasurements();

      return {
        'isInitialized': _metricsService.isInitialized,
        'isBoxOpen': isBoxOpen,
        'metricsCount': metrics.length,
        'hasSubscription': _metricsSubscription != null,
        'currentState': state.toString(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }

  // Add a method to force reload
  Future<void> forceReload() async {
    try {
      // Cancel existing subscription
      await _metricsSubscription?.cancel();
      _metricsSubscription = null;

      // Reinitialize the service
      await _metricsService.reinitialize();

      // Setup listener again
      _setupDataListener();

      // Load metrics
      add(LoadBodyMetrics());
    } catch (e) {
      print('Error in force reload: $e');
    }
  }
}
