import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class TimeSelectionDialog extends StatefulWidget {
  final String title;
  final double initialValue;
  final double maxValue;
  final String unit;
  final Color color;
  final IconData icon;
  final Function(double) onValueChanged;

  const TimeSelectionDialog({
    Key? key,
    required this.title,
    required this.initialValue,
    required this.maxValue,
    required this.unit,
    required this.color,
    required this.icon,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<TimeSelectionDialog> createState() => _TimeSelectionDialogState();
}

class _TimeSelectionDialogState extends State<TimeSelectionDialog> {
  late double _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
  }

  String _formatValue(double value) {
    if (widget.unit == 'h' || widget.unit == 'L') {
      return value.toStringAsFixed(1);
    } else {
      return value.toInt().toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: GoogleFonts.dmMono(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white70),
                  onPressed: () => GoRouter.of(context).pop(),
                ),
              ],
            ),
            Text(
              '${_formatValue(_currentValue)}${widget.unit}',
              style: TextStyle(
                color: widget.color,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: widget.color,
                inactiveTrackColor: Colors.grey[800],
                thumbColor: widget.color,
                overlayColor: widget.color.withOpacity(0.2),
              ),
              child: Slider(
                min: 0,
                max: widget.maxValue,
                value: _currentValue,
                divisions: widget.unit.isEmpty
                    ? widget.maxValue.toInt()
                    : // For steps (integer)
                    (widget.maxValue * 10)
                        .toInt(), // For hours/liters (decimal)
                onChanged: (value) {
                  setState(() {
                    _currentValue = value;
                  });
                },
              ),
            ),
            // Add preset buttons for quick selection
            if (widget.unit != '')
              Wrap(
                spacing: 10,
                children: [
                  for (double value in _getPresetValues())
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[800],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      onPressed: () {
                        setState(() {
                          _currentValue = value;
                        });
                      },
                      child: Text('${_formatValue(value)}${widget.unit}'),
                    ),
                ],
              ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => GoRouter.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.dmMono(color: Colors.white70),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    widget.onValueChanged(_currentValue);
                    GoRouter.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.color,
                  ),
                  child: Text(
                    'Save',
                    style: GoogleFonts.dmMono(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<double> _getPresetValues() {
    if (widget.unit == 'L') {
      return [0.5, 1.0, 1.5, 2.0];
    } else if (widget.unit == 'h') {
      return [0.5, 1.0, 1.5, 2.0, 2.5];
    } else {
      // For steps (using empty unit string)
      return [2500.0, 5000.0, 7500.0, 10000.0];
    }
  }
}
