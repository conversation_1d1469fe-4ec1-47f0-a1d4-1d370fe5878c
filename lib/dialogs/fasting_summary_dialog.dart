// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:confetti/confetti.dart';
import 'dart:math' as Math;
import 'package:fasttime/models/fasting_protocol.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class FastingSummaryDialog extends StatefulWidget {
  final DateTime startTime;
  final DateTime endTime;
  final int actualFastingMinutes;
  final FastingProtocol protocol;

  const FastingSummaryDialog({
    Key? key,
    required this.startTime,
    required this.endTime,
    required this.actualFastingMinutes,
    required this.protocol,
  }) : super(key: key);

  @override
  State<FastingSummaryDialog> createState() => _FastingSummaryDialogState();
}

class _FastingSummaryDialogState extends State<FastingSummaryDialog> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );
    // Start the confetti animation when dialog appears
    _confettiController.play();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  String _formatDuration(int minutes) {
    final hours = (minutes / 60).floor();
    final remainingMinutes = minutes % 60;
    return '${hours}h ${remainingMinutes}m';
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('MMM d, h:mm a').format(dateTime);
  }

  String _getAchievementMessage(int fastingMinutes, FastingProtocol protocol) {
    final targetMinutes = protocol.hours * 60;
    if (fastingMinutes >= targetMinutes) {
      return "Amazing! You've completed your ${protocol.name} fast!";
    } else if (fastingMinutes >= targetMinutes * 0.9) {
      return "Great job! You almost reached your ${protocol.name} goal!";
    } else if (fastingMinutes >= targetMinutes * 0.75) {
      return "Well done! You made significant progress!";
    } else {
      return "Good effort! Keep building your fasting habit!";
    }
  }

  double _calculateCaloriesBurned(int fastingMinutes) {
    // This is a simplified estimation
    // Assumes roughly 60-80 calories burned per hour of fasting beyond 12 hours
    final baseCalories = 1500; // Base metabolic rate calories for a day
    final hoursFasted = fastingMinutes / 60;

    if (hoursFasted <= 12) {
      return baseCalories / 2; // Roughly half daily BMR
    } else {
      final extraHours = hoursFasted - 12;
      return (baseCalories / 2) +
          (extraHours * 70); // 70 calories per extra hour
    }
  }

  int _calculateAutophagy(int fastingMinutes) {
    // Autophagy usually starts after 16-18 hours of fasting
    // This is a simplified score from 0-100
    final hoursFasted = fastingMinutes / 60;

    if (hoursFasted < 12) {
      return 0;
    } else if (hoursFasted < 16) {
      return ((hoursFasted - 12) / 4 * 25).round(); // 0-25%
    } else if (hoursFasted < 24) {
      return 25 + ((hoursFasted - 16) / 8 * 50).round(); // 25-75%
    } else {
      return 75 +
          ((hoursFasted - 24) / 12 * 25).round().clamp(0, 25); // 75-100%
    }
  }

  @override
  Widget build(BuildContext context) {
    final fastingDuration = _formatDuration(widget.actualFastingMinutes);
    final targetMinutes = widget.protocol.hours * 60;
    final achievedPercent = (widget.actualFastingMinutes / targetMinutes * 100)
        .clamp(0, 100)
        .round();
    final caloriesBurned =
        _calculateCaloriesBurned(widget.actualFastingMinutes).round();
    final autophagyScore = _calculateAutophagy(widget.actualFastingMinutes);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fasting Complete!',
                  style: GoogleFonts.dmMono(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white70),
                  onPressed: () => GoRouter.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Achievement message
            Text(
              _getAchievementMessage(
                  widget.actualFastingMinutes, widget.protocol),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Fasting details
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black12,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Protocol row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Protocol:',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        widget.protocol.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Duration row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Duration:',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        fastingDuration,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Start time row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Started:',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        _formatDateTime(widget.startTime),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // End time row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Ended:',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        _formatDateTime(widget.endTime),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Achievement indicators
            Row(
              children: [
                // Target achievement
                Expanded(
                  child: _buildAchievementCard(
                    title: 'Target',
                    value: '$achievedPercent%',
                    color: Colors.blue,
                    icon: Icons.flag,
                  ),
                ),
                const SizedBox(width: 12),

                // Calories burned
                Expanded(
                  child: _buildAchievementCard(
                    title: 'Calories',
                    value: '$caloriesBurned',
                    color: Colors.orange,
                    icon: Icons.local_fire_department,
                  ),
                ),
                const SizedBox(width: 12),

                // Autophagy score
                Expanded(
                  child: _buildAchievementCard(
                    title: 'Autophagy',
                    value: '$autophagyScore%',
                    color: Colors.purple,
                    icon: Icons.autorenew,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Continue button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => GoRouter.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  'Great!',
                  style: GoogleFonts.dmMono(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 22,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
