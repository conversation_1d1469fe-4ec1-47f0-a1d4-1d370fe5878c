import 'package:flutter/material.dart';
import 'package:fasttime/models/fasting_protocol.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ProtocolSelectionDialog extends StatelessWidget {
  final List<FastingProtocol> protocols;
  final String selectedProtocolId;

  const ProtocolSelectionDialog({
    Key? key,
    required this.protocols,
    required this.selectedProtocolId,
  }) : super(key: key);

  static Future<String?> show({
    required BuildContext context,
    required List<FastingProtocol> protocols,
    required String selectedProtocolId,
  }) async {
    return await showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FractionallySizedBox(
        heightFactor: 0.7,
        child: ProtocolSelectionDialog(
          protocols: protocols,
          selectedProtocolId: selectedProtocolId,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24.0, 16.0, 24.0, 24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHandle(),
              const SizedBox(height: 16),
              _buildHeader(context),
              const SizedBox(height: 24),
              Expanded(
                child: _buildProtocolsList(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHandle() {
    return Center(
      child: Container(
        width: 40,
        height: 5,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(2.5),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Select Protocol',
          style: GoogleFonts.dmSans(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () => GoRouter.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.white.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProtocolsList(BuildContext context) {
    return ListView.builder(
      itemCount: protocols.length,
      itemBuilder: (context, index) {
        final protocol = protocols[index];
        return _buildProtocolItem(context, protocol);
      },
    );
  }

  Widget _buildProtocolItem(BuildContext context, FastingProtocol protocol) {
    final isSelected = protocol.id == selectedProtocolId;
    final accentColor = const Color(0xFF6B4EFF);

    return GestureDetector(
      onTap: () {
        // Return the selected protocol ID directly
        Navigator.of(context).pop(protocol.id);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected
              ? accentColor.withOpacity(0.15)
              : Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected ? accentColor.withOpacity(0.5) : Colors.transparent,
            width: 1.5,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? accentColor : Colors.transparent,
                border: Border.all(
                  color: isSelected ? accentColor : Colors.white54,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 18)
                  : null,
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    protocol.name,
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    protocol.description,
                    style: GoogleFonts.dmSans(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildProtocolDetails(protocol),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProtocolDetails(FastingProtocol protocol) {
    // Extract fasting and eating hours from protocol ID (e.g., "16:8")
    final parts = protocol.id.split(':');
    final fastingHours = parts.isNotEmpty ? parts[0] : '';
    final eatingHours = parts.length > 1 ? parts[1] : '';

    return Row(
      children: [
        _buildDetailChip(
          icon: Icons.hourglass_empty,
          label: '$fastingHours hours',
          color: Colors.orange,
          tooltip: 'Fasting Window',
        ),
        const SizedBox(width: 12),
        _buildDetailChip(
          icon: Icons.restaurant,
          label: '$eatingHours hours',
          color: Colors.green,
          tooltip: 'Eating Window',
        ),
      ],
    );
  }

  Widget _buildDetailChip({
    required IconData icon,
    required String label,
    required Color color,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.dmSans(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
