import 'package:go_router/go_router.dart';
import '../screens/home/<USER>';
import '../screens/auth_page.dart';
import '../screens/settings_page.dart';
import '../screens/achievements_page.dart';
import '../screens/progress_page.dart';
import '../screens/body_metrics_page.dart';
import '../screens/splash/splash_screen.dart';
import '../screens/onboarding/onboarding_screen.dart';

import '../screens/change_password_page.dart';
import '../screens/terms_of_service_page.dart';
import '../screens/privacy_policy_page.dart';
import '../screens/about_author_page.dart';
import '../widgets/auth_wrapper.dart';
import '../widgets/auth_guard.dart';
import 'router_constants.dart';

final GoRouter router = GoRouter(
  initialLocation: '/',
  routes: [
    // Auth guard route - determines initial navigation based on auth status
    GoRoute(path: '/', builder: (context, state) => const AuthGuard()),
    // Splash screen route
    GoRoute(
      name: RouteConstants.splash,
      path: '/splash',
      builder: (context, state) => const SplashScreen(),
    ),
    // Onboarding screen route
    GoRoute(
      name: 'onboarding',
      path: '/onboarding',
      builder: (context, state) => const OnboardingScreen(),
    ),
    GoRoute(
      name: RouteConstants.signIn,
      path: '/sign-in',
      builder: (context, state) => const AuthPage(),
    ),
    GoRoute(
      name: RouteConstants.auth,
      path: '/auth',
      builder: (context, state) => const AuthPage(),
    ),
    // Auth wrapper for authenticated flow
    GoRoute(
      name: 'authWrapper',
      path: '/auth-wrapper',
      builder: (context, state) => const AuthWrapper(),
    ),

    // Private routes
    GoRoute(
      name: RouteConstants.home,
      path: '/home',
      builder: (context, state) => const HomePage(),
    ),
    GoRoute(
      name: RouteConstants.settings,
      path: '/settings',
      builder: (context, state) => const SettingsPage(),
    ),
    GoRoute(
      name: RouteConstants.achievements,
      path: '/achievements',
      builder: (context, state) => const AchievementsPage(),
    ),
    GoRoute(
      name: RouteConstants.progress,
      path: '/progress',
      builder: (context, state) => const ProgressPage(),
    ),
    GoRoute(
      name: RouteConstants.bodyMetrics,
      path: '/body-metrics',
      builder: (context, state) => const BodyMetricsPage(),
    ),

    GoRoute(
      name: RouteConstants.changePassword,
      path: '/change-password',
      builder: (context, state) => const ChangePasswordPage(),
    ),
    GoRoute(
      name: RouteConstants.termsOfService,
      path: '/terms-of-service',
      builder: (context, state) => const TermsOfServicePage(),
    ),
    GoRoute(
      name: RouteConstants.privacyPolicy,
      path: '/privacy-policy',
      builder: (context, state) => const PrivacyPolicyPage(),
    ),
    GoRoute(
      name: RouteConstants.aboutAuthor,
      path: '/about-author',
      builder: (context, state) => const AboutAuthorPage(),
    ),
  ],
);
