import 'dart:async';

/// A utility class to help manage multiple stream subscriptions
class SubscriptionManager {
  final List<StreamSubscription> _subscriptions = [];

  /// Add a subscription to be managed
  void add(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// Cancel all managed subscriptions
  void cancelAll() {
    for (var subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  /// Cancel a specific subscription and remove it from management
  void cancel(StreamSubscription subscription) {
    subscription.cancel();
    _subscriptions.remove(subscription);
  }
}