import 'package:fasttime/utils/time_utils.dart';
import 'package:flutter/material.dart';

import '../models/achievement.dart';
import '../models/fasting_day.dart';
import '../models/fasting_protocol.dart';
String getStatusText(FastingDay? fastingDay) {
    if (fastingDay == null ||
        fastingDay.startTime == '--:--' ||
        fastingDay.endTime == '--:--') {
      return "Set fasting window";
    }

    if (fastingDay.completed) {
      return "Completed!";
    }

    if (fastingDay.isActive) {
      return "Fasting time";
    } else {
      final now = DateTime.now();
      final startDateTime = getStartDateTime(fastingDay);

      if (now.isBefore(startDateTime)) {
        return "Starts in";
      } else {
        return "Ready to start";
      }
    }
  }

 Widget buildExtraTimeInfo(FastingDay? fastingDay) {
    if (fastingDay == null) return const SizedBox.shrink();

    final now = DateTime.now();
    final startDateTime = getStartDateTime(fastingDay);
    final targetDuration =
        Duration(hours: int.parse(fastingDay.fastingProtocol.split(':')[0]));
    final elapsed = now.difference(startDateTime);

    if (elapsed > targetDuration) {
      final overtime = elapsed - targetDuration;
      return Text(
        "+${formatDuration(overtime)} extra",
        style: const TextStyle(
          color: Colors.greenAccent,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
    }

    return Text(
      "${formatDuration(elapsed)} elapsed",
      style: const TextStyle(
        color: Colors.white60,
        fontSize: 14,
      ),
    );
  }

  String getNextFastInfo(FastingDay fastingDay) {
    final now = DateTime.now();
    final startDateTime = getStartDateTime(fastingDay);

    if (now.isBefore(startDateTime)) {
      final timeUntilStart = startDateTime.difference(now);
      return "Starts in ${formatDuration(timeUntilStart)}";
    }

    return "Ready to begin";
  }


String getBMICategory(double bmi) {
  if (bmi < 18.5) {
    return 'Underweight';
  } else if (bmi < 25) {
    return 'Normal';
  } else if (bmi < 30) {
    return 'Overweight';
  } else {
    return 'Obese';
  }
}

String getBMIDescription(double bmi) {
  if (bmi < 18.5) {
    return 'You are underweight. Consider consulting with a healthcare professional about healthy ways to gain weight.';
  } else if (bmi < 25) {
    return 'You have a healthy weight. Maintain your current lifestyle with balanced diet and regular exercise.';
  } else if (bmi < 30) {
    return 'You are overweight. Consider increasing physical activity and making dietary adjustments to reach a healthier weight.';
  } else {
    return 'You are in the obese category. It\'s recommended to consult with a healthcare professional about a weight management plan.';
  }
}
String formatRoutineValue(FastingDay? fastingDay, String type) {
  if (fastingDay == null) return "--/--";

  switch (type) {
    case 'Water':
      return "${fastingDay.waterIntake.toStringAsFixed(1)}/2L";
    case 'Steps':
      return "${fastingDay.steps}/10k";
    case 'Exercise':
      return "${fastingDay.exercise.toStringAsFixed(1)}/2.5h";
    default:
      return "--/--";
  }

}

// List of available fasting protocols
final List<FastingProtocol> availableProtocols = [
  FastingProtocol(
      id: '16:8',
      name: '16:8 Intermittent',
      hours: 16,
      description: 'Fast for 16 hours, eat during 8 hours'),
  FastingProtocol(
      id: '18:6',
      name: '18:6 Intermittent',
      hours: 18,
      description: 'Fast for 18 hours, eat during 6 hours'),
  FastingProtocol(
      id: '20:4',
      name: '20:4 Warrior',
      hours: 20,
      description: 'Fast for 20 hours, eat during 4 hours'),
  FastingProtocol(
      id: '24:0',
      name: 'OMAD',
      hours: 24,
      description: 'One Meal A Day (24 hour fast)'),
  FastingProtocol(
      id: '36:0', name: 'Extended', hours: 36, description: '36 hour fast'),
  FastingProtocol(
      id: '48:0', name: '2-Day', hours: 48, description: '2-day fast'),
];

// Fasting stages with their benefits and time frames
final List<Map<String, dynamic>> fastingStages = [
  {
    'name': 'Fed State',
    'hours': 0,
    'description': 'Body is digesting and absorbing food',
    'color': Colors.green,
  },
  {
    'name': 'Post-Absorptive',
    'hours': 4,
    'description': 'Blood sugar begins to drop, glycogen stores used',
    'color': Colors.lime,
  },
  {
    'name': 'Early Fasting',
    'hours': 8,
    'description': 'Glucose depleting, fat burning beginning',
    'color': Colors.amber,
  },
  {
    'name': 'Fat Burning',
    'hours': 12,
    'description': 'Fat burning mode fully activated',
    'color': Colors.orange,
  },
  {
    'name': 'Ketosis',
    'hours': 16,
    'description': 'Ketone production increased, mental clarity',
    'color': Colors.deepOrange,
  },
  {
    'name': 'Autophagy',
    'hours': 24,
    'description': 'Cellular cleanup and repair processes activated',
    'color': Colors.red,
  },
  {
    'name': 'Growth Hormone',
    'hours': 36,
    'description': 'Increased growth hormone secretion',
    'color': Colors.purple,
  },
  {
    'name': 'Deep Autophagy',
    'hours': 48,
    'description': 'Enhanced autophagy and immune system regeneration',
    'color': Colors.indigo,
  },
];
