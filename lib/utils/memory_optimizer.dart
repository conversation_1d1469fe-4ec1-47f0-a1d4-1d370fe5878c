import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

/// Emergency memory optimization utility to prevent app crashes
class MemoryOptimizer {
  static final MemoryOptimizer _instance = MemoryOptimizer._internal();
  factory MemoryOptimizer() => _instance;
  MemoryOptimizer._internal();

  Timer? _memoryCheckTimer;
  bool _isLowMemoryMode = false;
  final List<VoidCallback> _lowMemoryCallbacks = [];

  /// Start monitoring memory and trigger optimizations when needed
  void startMemoryMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = Timer.periodic(
      const Duration(seconds: 10), // Check every 10 seconds
      (_) => _checkMemoryStatus(),
    );
  }

  /// Stop memory monitoring
  void stopMemoryMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
  }

  /// Check current memory status and trigger optimizations if needed
  Future<void> _checkMemoryStatus() async {
    try {
      // Force garbage collection
      if (_isLowMemoryMode) {
        _forceGarbageCollection();
      }

      // Check if we should enter low memory mode
      final shouldEnterLowMemory = await _shouldEnterLowMemoryMode();

      if (shouldEnterLowMemory && !_isLowMemoryMode) {
        _enterLowMemoryMode();
      } else if (!shouldEnterLowMemory && _isLowMemoryMode) {
        _exitLowMemoryMode();
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log('Error in memory check: $e', name: 'MemoryOptimizer');
      }
    }
  }

  /// Determine if we should enter low memory mode
  Future<bool> _shouldEnterLowMemoryMode() async {
    try {
      // On Android, we can check available memory
      if (Platform.isAndroid) {
        final result = Process.runSync('cat', ['/proc/meminfo']);
        if (result.exitCode == 0) {
          final memInfo = result.stdout.toString();
          final availableMatch = RegExp(
            r'MemAvailable:\s*(\d+)\s*kB',
          ).firstMatch(memInfo);
          if (availableMatch != null) {
            final availableKB = int.parse(availableMatch.group(1)!);
            final availableMB = availableKB / 1024;

            // Enter low memory mode if less than 100MB available
            return availableMB < 100;
          }
        }
      }
    } catch (e) {
      // If we can't check memory, assume we're in low memory mode to be safe
      return true;
    }

    // Default to low memory mode for safety
    return false;
  }

  /// Enter low memory mode - trigger all optimizations
  void _enterLowMemoryMode() {
    if (_isLowMemoryMode) return;

    _isLowMemoryMode = true;

    if (kDebugMode) {
      developer.log('Entering low memory mode', name: 'MemoryOptimizer');
    }

    // Trigger all registered callbacks
    for (final callback in _lowMemoryCallbacks) {
      try {
        callback();
      } catch (e) {
        if (kDebugMode) {
          developer.log(
            'Error in low memory callback: $e',
            name: 'MemoryOptimizer',
          );
        }
      }
    }

    // Force garbage collection
    _forceGarbageCollection();
  }

  /// Exit low memory mode
  void _exitLowMemoryMode() {
    if (!_isLowMemoryMode) return;

    _isLowMemoryMode = false;

    if (kDebugMode) {
      developer.log('Exiting low memory mode', name: 'MemoryOptimizer');
    }
  }

  /// Force garbage collection
  void _forceGarbageCollection() {
    try {
      // Force garbage collection in Dart
      for (int i = 0; i < 3; i++) {
        List.generate(1000, (index) => index).clear();
      }
    } catch (e) {
      // Ignore errors during GC
    }
  }

  /// Register a callback to be called when entering low memory mode
  void registerLowMemoryCallback(VoidCallback callback) {
    _lowMemoryCallbacks.add(callback);
  }

  /// Unregister a low memory callback
  void unregisterLowMemoryCallback(VoidCallback callback) {
    _lowMemoryCallbacks.remove(callback);
  }

  /// Check if currently in low memory mode
  bool get isLowMemoryMode => _isLowMemoryMode;

  /// Force enter low memory mode (for testing or emergency situations)
  void forceEnterLowMemoryMode() {
    // Reset state first to ensure callbacks are triggered
    _isLowMemoryMode = false;
    _enterLowMemoryMode();
  }

  /// Emergency memory cleanup - call this when out of memory errors occur
  void emergencyCleanup() {
    if (kDebugMode) {
      developer.log(
        'Emergency memory cleanup triggered',
        name: 'MemoryOptimizer',
      );
    }

    // Force low memory mode
    _isLowMemoryMode = true;

    // Trigger all callbacks
    for (final callback in _lowMemoryCallbacks) {
      try {
        callback();
      } catch (e) {
        // Ignore errors during emergency cleanup
      }
    }

    // Aggressive garbage collection
    for (int i = 0; i < 10; i++) {
      _forceGarbageCollection();
    }
  }

  /// Dispose resources
  void dispose() {
    stopMemoryMonitoring();
    _lowMemoryCallbacks.clear();
  }
}

/// Mixin for widgets to automatically handle low memory situations
mixin LowMemoryHandlingMixin<T extends StatefulWidget> on State<T> {
  late VoidCallback _lowMemoryCallback;

  @override
  void initState() {
    super.initState();
    _lowMemoryCallback = _handleLowMemory;
    MemoryOptimizer().registerLowMemoryCallback(_lowMemoryCallback);
  }

  @override
  void dispose() {
    MemoryOptimizer().unregisterLowMemoryCallback(_lowMemoryCallback);
    super.dispose();
  }

  /// Override this method to handle low memory situations
  void _handleLowMemory() {
    // Default implementation - override in subclasses
    if (mounted) {
      setState(() {
        // Trigger rebuild to potentially free memory
      });
    }
  }

  /// Override this method to provide custom low memory handling
  void onLowMemory() {
    _handleLowMemory();
  }
}
