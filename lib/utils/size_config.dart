import 'package:flutter/widgets.dart';

class SizeConfig {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static late double blockSizeHorizontal;
  static late double blockSizeVertical;

  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;
  }

  /// Returns a dynamic height based on the screen height percentage
  static double height(double percent) {
    return blockSizeVertical * percent;
  }

  /// Returns a dynamic width based on the screen width percentage
  static double width(double percent) {
    return blockSizeHorizontal * percent;
  }

  /// Returns a SizedBox with dynamic height
  static SizedBox sizedBoxHeight(double percent) {
    return SizedBox(height: height(percent));
  }

  /// Returns a SizedBox with dynamic width
  static SizedBox sizedBoxWidth(double percent) {
    return SizedBox(width: width(percent));
  }

  /// Common dynamic SizedBoxes for vertical spacing
  static SizedBox h4(BuildContext context) => sizedBoxHeight(0.5); // ~4px on 800px height
  static SizedBox h8(BuildContext context) => sizedBoxHeight(1);   // ~8px on 800px height
  static SizedBox h16(BuildContext context) => sizedBoxHeight(2);  // ~16px on 800px height
  static SizedBox h24(BuildContext context) => sizedBoxHeight(3);  // ~24px on 800px height
  static SizedBox h32(BuildContext context) => sizedBoxHeight(4);  // ~32px on 800px height
  static SizedBox h48(BuildContext context) => sizedBoxHeight(6);  // ~48px on 800px height
  static SizedBox h64(BuildContext context) => sizedBoxHeight(8);  // ~64px on 800px height
}
