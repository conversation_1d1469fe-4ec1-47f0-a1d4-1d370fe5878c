  import 'package:flutter/material.dart';

Color getProgressColor(double progress) {
    if (progress <= 0.25) {
      return  Color.fromRGBO(252,150,1,1); //
    } else if (progress <= 0.75) {
      return const Color(0xFFFFA726); // Orange
    } else {
      return const Color(0xFF66BB6A); // Green
    }
  }

  Color getAnimatedColor(double progress, double pulseValue) {
    final baseColor = getProgressColor(progress);
    final hslColor = HSLColor.fromColor(baseColor);

    // Animate lightness and saturation
    final animatedColor = hslColor
        .withLightness(
            (hslColor.lightness + (pulseValue * 0.1)).clamp(0.0, 1.0))
        .withSaturation(
            (hslColor.saturation + (pulseValue * 0.1)).clamp(0.0, 1.0));

    return animatedColor.toColor();
  }

  Color getBMIColor(double bmi) {
    if (bmi < 18.5) {
      return Colors.blue;
    } else if (bmi < 25) {
      return Colors.green;
    } else if (bmi < 30) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }