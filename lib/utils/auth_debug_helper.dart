import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_persistence_service.dart';

/// Helper class for debugging authentication state and persistence
class AuthDebugHelper {
  /// Print current authentication status
  static Future<void> printAuthStatus() async {
    print('=== Authentication Debug Info ===');
    
    // Firebase Auth status
    final firebaseUser = FirebaseAuth.instance.currentUser;
    print('Firebase User: ${firebaseUser?.uid ?? 'null'}');
    print('Firebase Email: ${firebaseUser?.email ?? 'null'}');
    print('Firebase Display Name: ${firebaseUser?.displayName ?? 'null'}');
    
    // Stored data status
    final storedData = await AuthPersistenceService.getStoredUserData();
    print('Stored User ID: ${storedData?['userId'] ?? 'null'}');
    print('Stored Email: ${storedData?['email'] ?? 'null'}');
    print('Stored Display Name: ${storedData?['displayName'] ?? 'null'}');
    
    // Session validity
    final isSessionValid = await AuthPersistenceService.isSessionValid();
    print('Session Valid: $isSessionValid');
    
    // First launch status
    final isFirstLaunch = await AuthPersistenceService.isFirstLaunch();
    print('Is First Launch: $isFirstLaunch');
    
    // Auth stats
    final authStats = await AuthPersistenceService.getAuthStats();
    print('Auth Stats: $authStats');
    
    print('=== End Debug Info ===');
  }
  
  /// Verify authentication integrity
  static Future<bool> verifyAuthIntegrity() async {
    final firebaseUser = FirebaseAuth.instance.currentUser;
    if (firebaseUser == null) {
      print('No Firebase user found');
      return false;
    }
    
    final isDataValid = await AuthPersistenceService.verifyUserDataIntegrity(firebaseUser);
    final isSessionValid = await AuthPersistenceService.isSessionValid();
    
    print('Data integrity: $isDataValid');
    print('Session validity: $isSessionValid');
    
    return isDataValid && isSessionValid;
  }
  
  /// Clear all authentication data (for testing)
  static Future<void> clearAllAuthData() async {
    await FirebaseAuth.instance.signOut();
    await AuthPersistenceService.clearAuthData();
    print('All authentication data cleared');
  }
}
