import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

/// A utility class to monitor app performance and memory usage
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  Timer? _monitoringTimer;
  final List<PerformanceMetric> _metrics = [];
  final int _maxMetrics = 20; // Reduced to save memory

  bool _isMonitoring = false;

  /// Start monitoring performance with reduced frequency to save memory
  void startMonitoring({Duration interval = const Duration(minutes: 2)}) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(interval, (_) => _collectMetrics());

    if (kDebugMode) {
      developer.log(
        'Performance monitoring started with ${interval.inMinutes}min interval',
        name: 'PerformanceMonitor',
      );
    }
  }

  /// Stop monitoring performance
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;

    if (kDebugMode) {
      developer.log(
        'Performance monitoring stopped',
        name: 'PerformanceMonitor',
      );
    }
  }

  /// Collect current performance metrics
  Future<void> _collectMetrics() async {
    try {
      final metric = PerformanceMetric(
        timestamp: DateTime.now(),
        memoryUsage: await _getMemoryUsage(),
        frameRate: await _getFrameRate(),
      );

      _metrics.add(metric);

      // Keep only the last N metrics to prevent memory growth
      if (_metrics.length > _maxMetrics) {
        _metrics.removeAt(0);
      }

      // Log warning if memory usage is high
      if (metric.memoryUsage > 100) {
        // 100MB threshold
        developer.log(
          'High memory usage detected: ${metric.memoryUsage.toStringAsFixed(1)}MB',
          name: 'PerformanceMonitor',
          level: 900, // Warning level
        );
      }

      // Log warning if frame rate is low
      if (metric.frameRate < 50) {
        // 50 FPS threshold
        developer.log(
          'Low frame rate detected: ${metric.frameRate.toStringAsFixed(1)} FPS',
          name: 'PerformanceMonitor',
          level: 900, // Warning level
        );
      }
    } catch (e) {
      if (kDebugMode) {
        developer.log(
          'Error collecting metrics: $e',
          name: 'PerformanceMonitor',
        );
      }
    }
  }

  /// Get current memory usage in MB
  Future<double> _getMemoryUsage() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // Use platform channel to get memory info
        const platform = MethodChannel('performance_monitor');
        final result = await platform.invokeMethod('getMemoryUsage');
        return (result as num).toDouble();
      }
    } catch (e) {
      // Fallback: estimate based on Dart VM info
      if (kDebugMode) {
        developer.log(
          'Could not get platform memory usage: $e',
          name: 'PerformanceMonitor',
        );
      }
    }

    // Return a default value if platform-specific method fails
    return 0.0;
  }

  /// Get current frame rate
  Future<double> _getFrameRate() async {
    // This is a simplified implementation
    // In a real app, you might want to use more sophisticated frame rate monitoring
    return 60.0; // Default to 60 FPS
  }

  /// Get performance summary
  PerformanceSummary getSummary() {
    if (_metrics.isEmpty) {
      return PerformanceSummary(
        averageMemoryUsage: 0,
        maxMemoryUsage: 0,
        averageFrameRate: 0,
        minFrameRate: 0,
        sampleCount: 0,
      );
    }

    final memoryUsages = _metrics.map((m) => m.memoryUsage).toList();
    final frameRates = _metrics.map((m) => m.frameRate).toList();

    return PerformanceSummary(
      averageMemoryUsage:
          memoryUsages.reduce((a, b) => a + b) / memoryUsages.length,
      maxMemoryUsage: memoryUsages.reduce((a, b) => a > b ? a : b),
      averageFrameRate: frameRates.reduce((a, b) => a + b) / frameRates.length,
      minFrameRate: frameRates.reduce((a, b) => a < b ? a : b),
      sampleCount: _metrics.length,
    );
  }

  /// Get recent metrics
  List<PerformanceMetric> getRecentMetrics({int count = 10}) {
    final startIndex = _metrics.length > count ? _metrics.length - count : 0;
    return _metrics.sublist(startIndex);
  }

  /// Log performance summary
  void logSummary() {
    final summary = getSummary();
    if (kDebugMode) {
      developer.log(
        'Performance Summary:\n'
        '  Average Memory: ${summary.averageMemoryUsage.toStringAsFixed(1)}MB\n'
        '  Max Memory: ${summary.maxMemoryUsage.toStringAsFixed(1)}MB\n'
        '  Average FPS: ${summary.averageFrameRate.toStringAsFixed(1)}\n'
        '  Min FPS: ${summary.minFrameRate.toStringAsFixed(1)}\n'
        '  Samples: ${summary.sampleCount}',
        name: 'PerformanceMonitor',
      );
    }
  }

  /// Check if performance is healthy
  bool isPerformanceHealthy() {
    final summary = getSummary();
    return summary.averageMemoryUsage < 150 && // Less than 150MB average
        summary.maxMemoryUsage < 200 && // Less than 200MB max
        summary.averageFrameRate > 45; // More than 45 FPS average
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _metrics.clear();
  }
}

/// Represents a single performance metric sample
class PerformanceMetric {
  final DateTime timestamp;
  final double memoryUsage; // in MB
  final double frameRate; // in FPS

  PerformanceMetric({
    required this.timestamp,
    required this.memoryUsage,
    required this.frameRate,
  });

  @override
  String toString() {
    return 'PerformanceMetric(timestamp: $timestamp, memory: ${memoryUsage.toStringAsFixed(1)}MB, fps: ${frameRate.toStringAsFixed(1)})';
  }
}

/// Summary of performance metrics over time
class PerformanceSummary {
  final double averageMemoryUsage;
  final double maxMemoryUsage;
  final double averageFrameRate;
  final double minFrameRate;
  final int sampleCount;

  PerformanceSummary({
    required this.averageMemoryUsage,
    required this.maxMemoryUsage,
    required this.averageFrameRate,
    required this.minFrameRate,
    required this.sampleCount,
  });

  @override
  String toString() {
    return 'PerformanceSummary(avgMemory: ${averageMemoryUsage.toStringAsFixed(1)}MB, '
        'maxMemory: ${maxMemoryUsage.toStringAsFixed(1)}MB, '
        'avgFPS: ${averageFrameRate.toStringAsFixed(1)}, '
        'minFPS: ${minFrameRate.toStringAsFixed(1)}, '
        'samples: $sampleCount)';
  }
}

/// Widget mixin to automatically monitor performance
mixin PerformanceMonitoringMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();
    PerformanceMonitor().startMonitoring();
  }

  @override
  void dispose() {
    // Don't stop monitoring here as other widgets might be using it
    super.dispose();
  }
}
