import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

/// A utility class to help manage memory usage and prevent memory leaks
class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  final List<StreamSubscription> _subscriptions = [];
  final List<Timer> _timers = [];
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // Cache expiry time in minutes
  static const int _cacheExpiryMinutes = 5;

  /// Add a stream subscription to be managed
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// Add a timer to be managed
  void addTimer(Timer timer) {
    _timers.add(timer);
  }

  /// Cache data with automatic expiry
  void cacheData(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    _cleanExpiredCache();
  }

  /// Get cached data if it exists and hasn't expired
  T? getCachedData<T>(String key) {
    if (!_cache.containsKey(key)) return null;

    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;

    final now = DateTime.now();
    if (now.difference(timestamp).inMinutes > _cacheExpiryMinutes) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }

    return _cache[key] as T?;
  }

  /// Clean expired cache entries
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp).inMinutes > _cacheExpiryMinutes) {
        expiredKeys.add(key);
      }
    });

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  /// Cancel all managed subscriptions
  void cancelAllSubscriptions() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  /// Cancel all managed timers
  void cancelAllTimers() {
    for (final timer in _timers) {
      timer.cancel();
    }
    _timers.clear();
  }

  /// Clear all cache
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// Dispose all resources
  void dispose() {
    cancelAllSubscriptions();
    cancelAllTimers();
    clearCache();
  }

  /// Log memory usage information
  void logMemoryUsage(String context) {
    if (kDebugMode) {
      developer.log(
        'Memory Usage - Context: $context, '
        'Subscriptions: ${_subscriptions.length}, '
        'Timers: ${_timers.length}, '
        'Cache entries: ${_cache.length}',
        name: 'MemoryManager',
      );
    }
  }

  /// Get memory usage statistics
  Map<String, int> getMemoryStats() {
    return {
      'subscriptions': _subscriptions.length,
      'timers': _timers.length,
      'cacheEntries': _cache.length,
    };
  }
}

/// A mixin to help widgets manage their resources automatically
mixin MemoryManagementMixin<T extends StatefulWidget> on State<T> {
  final List<StreamSubscription> _localSubscriptions = [];
  final List<Timer> _localTimers = [];

  /// Add a subscription to be automatically disposed
  void addManagedSubscription(StreamSubscription subscription) {
    _localSubscriptions.add(subscription);
  }

  /// Add a timer to be automatically disposed
  void addManagedTimer(Timer timer) {
    _localTimers.add(timer);
  }

  @override
  void dispose() {
    // Cancel all local subscriptions
    for (final subscription in _localSubscriptions) {
      subscription.cancel();
    }
    _localSubscriptions.clear();

    // Cancel all local timers
    for (final timer in _localTimers) {
      timer.cancel();
    }
    _localTimers.clear();

    super.dispose();
  }
}

/// A utility class for efficient data caching with size limits
class LimitedCache<K, V> {
  final int maxSize;
  final Map<K, V> _cache = {};
  final Map<K, DateTime> _accessTimes = {};

  LimitedCache({this.maxSize = 100});

  void put(K key, V value) {
    if (_cache.length >= maxSize && !_cache.containsKey(key)) {
      _evictLeastRecentlyUsed();
    }

    _cache[key] = value;
    _accessTimes[key] = DateTime.now();
  }

  V? get(K key) {
    if (_cache.containsKey(key)) {
      _accessTimes[key] = DateTime.now();
      return _cache[key];
    }
    return null;
  }

  void _evictLeastRecentlyUsed() {
    if (_accessTimes.isEmpty) return;

    K? oldestKey;
    DateTime? oldestTime;

    _accessTimes.forEach((key, time) {
      if (oldestTime == null || time.isBefore(oldestTime!)) {
        oldestKey = key;
        oldestTime = time;
      }
    });

    if (oldestKey != null) {
      _cache.remove(oldestKey);
      _accessTimes.remove(oldestKey);
    }
  }

  void clear() {
    _cache.clear();
    _accessTimes.clear();
  }

  int get size => _cache.length;
}
