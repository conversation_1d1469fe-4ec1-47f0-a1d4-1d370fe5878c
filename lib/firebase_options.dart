// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBVc3aVDuySG6fZVdsWCDz-z0LFfVrNKr8',
    appId: '1:847434759662:android:3bcf120c73e1013ddc22ff',
    messagingSenderId: '847434759662',
    projectId: 'fasttime-63458',
    databaseURL: 'https://fasttime-63458-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'fasttime-63458.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyASR0MipicDaNyE08gZ5n86dsuLvqAn9pw',
    appId: '1:847434759662:ios:b3beddb14343c1ebdc22ff',
    messagingSenderId: '847434759662',
    projectId: 'fasttime-63458',
    databaseURL: 'https://fasttime-63458-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'fasttime-63458.firebasestorage.app',
    androidClientId: '847434759662-1uueaq6sp86ehkj8q1v67s6rbe4tqdgq.apps.googleusercontent.com',
    iosClientId: '847434759662-2ndnjdhcsh5no7p8oss7g766q341gpt6.apps.googleusercontent.com',
    iosBundleId: 'com.all3n2601.fasttime',
  );
}
