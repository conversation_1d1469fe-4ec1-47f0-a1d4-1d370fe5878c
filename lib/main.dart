import 'package:fasttime/utils/performance_monitor.dart';
import 'package:fasttime/utils/memory_optimizer.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';

// Bloc imports
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/body_metrics/body_metrics_bloc.dart';
import 'blocs/achievement/achievement_bloc.dart';
import 'blocs/settings/settings_bloc.dart';
import 'blocs/timer/timer_bloc.dart';
import 'blocs/fasting/fasting_bloc.dart';

// Service imports
import 'services/body_metrics_service.dart';
import 'services/achievement_service.dart';
import 'services/fasting_service.dart';

// Model adapters
import 'models/achievement.dart' show AchievementAdapter;
import 'models/body_metric.dart' show BodyMetricAdapter;
import 'models/fasting_day.dart' show FastingDayAdapter;

// Router
import 'route/router_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Start memory monitoring first to prevent crashes
  MemoryOptimizer().startMemoryMonitoring();

  // Start performance monitoring with reduced frequency
  PerformanceMonitor().startMonitoring();

  // Initialize Firebase first
  await Firebase.initializeApp();

  // Initialize Hive with adapters
  await _initializeHive();

  runApp(
    MultiBlocProvider(
      providers: [
        // Auth Bloc
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc()..add(AuthCheckRequested()),
        ),

        // Body Metrics Bloc with service dependency
        BlocProvider<BodyMetricsBloc>(
          create: (context) => BodyMetricsBloc(BodyMetricsService()),
        ),

        // Achievement Bloc with service dependencies
        BlocProvider<AchievementBloc>(
          create: (context) =>
              AchievementBloc(AchievementService(), FastingService()),
        ),

        // Settings Bloc
        BlocProvider<SettingsBloc>(create: (context) => SettingsBloc()),

        // Timer Bloc
        BlocProvider<TimerBloc>(create: (context) => TimerBloc()),

        // Fasting Bloc with service dependency
        BlocProvider<FastingBloc>(
          create: (context) => FastingBloc(FastingService()),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

Future<void> _initializeHive() async {
  await Hive.initFlutter();

  // Register adapters only once
  if (!Hive.isAdapterRegistered(0)) {
    Hive.registerAdapter(FastingDayAdapter());
  }

  if (!Hive.isAdapterRegistered(1)) {
    Hive.registerAdapter(BodyMetricAdapter());
  }

  if (!Hive.isAdapterRegistered(2)) {
    Hive.registerAdapter(AchievementAdapter());
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'FastTime',
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: GoogleFonts.dmSans().fontFamily,
        scaffoldBackgroundColor: const Color(0xFF121212),
        // Add page transitions for smoother navigation
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
    );
  }
}
